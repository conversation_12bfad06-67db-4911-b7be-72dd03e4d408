import type { z } from "zod";
import { employeesSchema } from "./employee-schema";
import { expenditureSchema } from "./expenditure-schema";
import { incomeSchema } from "./income-schema";
import { lawsRegulationsSchema } from "./laws-regulations-schema";
import { premisesSchema } from "./premises-schema";

export const holdingBusinessSchema = incomeSchema
  .and(expenditureSchema)
  .and(employeesSchema)
  .and(premisesSchema)
  .and(lawsRegulationsSchema)

export type HoldingBusinessSchemaType = z.infer<typeof holdingBusinessSchema>
