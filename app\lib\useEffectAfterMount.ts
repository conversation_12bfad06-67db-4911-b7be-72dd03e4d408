import { useEffect, useRef } from "react";

export const useEffectAfterMount: typeof useEffect = (effect, deps) => {
  const nthMount = useRef(0);

  useEffect(() => {
    if (process.env.NODE_ENV === "development" && nthMount.current >= 2) {
      return effect()
    }

    if (process.env.NODE_ENV === "production" && nthMount.current >= 1) {
      return effect();
    }

    nthMount.current += 1
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps);
};
