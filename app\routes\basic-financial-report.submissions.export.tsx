import { searchSchema } from "~/features/basic-financial-report/schemas/searchSchema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import { managementGenerateSubmissionsReport } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, setNotification, redirect, queryString }) => {
  await middleware(["auth"], request);
  const { module: bfrModule } = await requireActiveModule({ request, key: Modules.BASIC_FINANCIAL_REPORT });
  const schemaData = searchSchema.safeParse(queryString).data
  const location = schemaData?.location
  if (!location) {
    throw new Error("A valid location is required to process the error. Please ensure the location is provided.");
  }

  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });
  const isUsingAccountingRecordsTool = schemaData?.usingAccountingRecordsOnly === "true" ? true : schemaData?.usingAccountingRecordsOnly === "false" ? false : undefined
  const { data, error, response } = await managementGenerateSubmissionsReport({ headers: await authHeaders(request), query: {
    ModuleId: bfrModule.id,
    PageNumber: pageNumber,
    GeneralSearchTerm: schemaData?.search,
    SortOrder: orderDirection,
    SortBy: order as any,
    PageSize: pageSize,
    SubmittedAfterDate: schemaData?.submittedAfter,
    SubmittedBeforeDate: schemaData?.submittedBefore,
    FinancialPeriodStartAt: schemaData?.financialPeriodStart,
    FinancialPeriodEndAt: schemaData?.financialPeriodEnd,
    IsUsingAccountingRecordsTool: isUsingAccountingRecordsTool,

  } })
  if (error) {
    setNotification({ title: "Error!", message: error.exceptionMessage as string || undefined, variant: "error" });

    return redirect(location);
  }

  // Whitelist specific headers
  const whitelistedHeaders = new Headers();
  const allowedHeaders = ["Content-Type", "Content-Disposition"];
  for (const header of allowedHeaders) {
    const value = response.headers.get(header);
    if (value) {
      whitelistedHeaders.set(header, value);
    }
  }

  return new Response(data, {
    status: response.status,
    headers: whitelistedHeaders,
  });
}, { authorize: ["bfr.panama.submissions.export"] });
