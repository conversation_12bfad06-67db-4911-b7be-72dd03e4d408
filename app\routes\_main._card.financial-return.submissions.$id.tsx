import type { ReactNode } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { <PERSON> } from "@remix-run/react";
import { differenceInMonths } from "date-fns";
import { FileDown, ScrollText } from "lucide-react";
import { useRemixForm } from "remix-hook-form";
import { ActionSheetActionRow } from "~/components/ActionSheetActionRow";
import { ActionSheetBody } from "~/components/ActionSheetBody";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetDescriptionList } from "~/components/ActionSheetDescriptionList";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { ActionSheetSection } from "~/components/ActionSheetSection";
import { Form } from "~/components/Form";
import { FormDatePicker } from "~/components/FormDatePicker";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";

export const loader = makeEnhancedLoader(async ({ json }) => {
  return json({ financialReturn: {

  } })
});

function canChangeFinancialPeriod() {
  const financialPeriodEnd = new Date("2025-12-31")
  const submittedAt = new Date("2025-01-01")
  const isPenalty = false;

  return differenceInMonths(financialPeriodEnd, submittedAt) <= 9 && !isPenalty
}

function canDownloadReport() {
  const status = "Completed"
  const allowedStatuses = ["Information request", "Completed", "Under Review", "Confirmed", "Help request", "Help completed"]

  return allowedStatuses.includes(status)
}

function canChangeStartDate() {
  const isSubsequentSubmission = false;

  return !isSubsequentSubmission;
}

export default function FinancialReturnDetail(): ReactNode {
  const formMethods = useRemixForm({
    mode: "onSubmit",
    defaultValues: {
      fps: new Date("2025-01-01").toISOString(),
      fpe: new Date("2025-12-31").toISOString(),
    },
  })

  return (
    <Form formMethods={formMethods} remixFormProps={{ method: "post", className: "h-full" }}>
      <ActionSheetBody>
        <ActionSheetContent title="Edit financial return">
          <ActionSheetSection title="Details">
            <ActionSheetDescriptionList data={[]} headers={[]} />
          </ActionSheetSection>
          <ActionSheetSection title="Action">
            <ActionSheetActionRow label="History">
              <Button
                asChild
                size="sm"
                variant="outline"
                type="button"
                className="self-center text-sm flex items-center justify-center gap-1.5 mr-2"
              >
                <Link to="./log">
                  <ScrollText size={14} className="text-blue-600" />
                  <span className="text-xs font-semibold">View Log</span>
                </Link>
              </Button>

            </ActionSheetActionRow>
            <ActionSheetSection title="Settings">
              <FormDatePicker name="fps" label="Financial period start date" datePickerProps={{ disabled: !canChangeFinancialPeriod() || !canChangeStartDate() }} />
              <FormDatePicker name="fpe" label="Financial period end date" datePickerProps={{ disabled: !canChangeFinancialPeriod() }} />
            </ActionSheetSection>
            <ActionSheetSection title="Documents">
              <Button className="inline-flex items-center w-auto" type="button" size="sm" disabled={!canDownloadReport()} asChild>
                <Link
                  to="#"
                  target="_blank"
                >
                  <FileDown size={14} className="mr-2" />
                  <span className="text-xs font-semibold">Download financial return</span>
                </Link>
              </Button>
            </ActionSheetSection>
          </ActionSheetSection>
        </ActionSheetContent>
        <ActionSheetFooter>
        </ActionSheetFooter>
      </ActionSheetBody>
    </Form>
  );
}
