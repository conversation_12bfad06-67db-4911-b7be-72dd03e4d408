import type { ReactNode } from "react";
import { PaymentImportContainer } from "~/features/simplified-tax-return/payment-import/components/PaymentImportContainer";
import { ImportPaymentProvider } from "~/features/simplified-tax-return/payment-import/context/ImportPaymentContext";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";

export const handle = {
  breadcrumb: {
    label: "Bulk import",
    to: "/simplified-tax-return/payments/import",
  },
  title: "Bulk import",
}

export const loader = makeEnhancedLoader(async ({ request }) => {
  await middleware(["auth"], request);

  return null;
}, { authorize: ["str.payments.import"] });

export default function SimplifiedTaxReturnBulkImport(): ReactNode {
  return (
    <ImportPaymentProvider>
      <PaymentImportContainer />
    </ImportPaymentProvider>
  )
}
