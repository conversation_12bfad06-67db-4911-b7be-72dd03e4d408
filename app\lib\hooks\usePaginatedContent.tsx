import type { ReactNode } from "react";
import { cn } from "@netpro/design-system";
import {
  Fragment,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";

// Create an isomorphic hook that falls back to useEffect on the server.
const useIsomorphicLayoutEffect
  = typeof window !== "undefined" ? useLayoutEffect : useEffect;

/**
 * Options for paginating content.
 * @property contentHeight - Maximum height (in pixels) allowed for content per page.
 */
export type PaginatedContentOptions = {
  contentHeight: number
};

/**
 * The result returned by the usePaginatedContent hook.
 * @property pages - An array of ReactNodes representing the paginated pages.
 * @property measurementContainer - A hidden container used for measuring content height.
 */
export type PaginatedContentResult = {
  pages: ReactNode[]
  measurementContainer: ReactNode
};

/**
 * Interface for an item that has been measured.
 * @property key - A unique key for the item.
 * @property content - The original ReactNode content.
 * @property height - The measured height of the item (in pixels).
 */
type MeasuredItem = {
  key: string
  content: ReactNode
  height: number
};

/**
 * Hook that paginates an array of ReactNodes based on a maximum content height.
 *
 * The hook first renders all the content off-screen in a hidden container to measure the height of each item.
 * Then, it groups the items into pages such that the total height of each page does not exceed the specified contentHeight.
 *
 * @param content - The array of ReactNode items to paginate.
 * @param options - Pagination options.
 * @param options.contentHeight - Maximum content height (in pixels) allowed for each page.
 * @returns An object containing the paginated pages and the hidden measurement container.
 */
export function usePaginatedContent(
  content: ReactNode[],
  { contentHeight }: PaginatedContentOptions,
): PaginatedContentResult {
  // Reference to the hidden measurement container.
  const measurementRef = useRef<HTMLDivElement>(null);
  // State for the rendered pages.
  const [pages, setPages] = useState<ReactNode[]>([]);
  /**
   * Generate a stable key for each content item.
   * If the content already has a key, use it; otherwise generate a new UUID.
   */
  const contentKeys = useMemo(
    () =>
      content.map((child) => {
        if (
          typeof child === "object"
          && child !== null
          && "key" in child
          && child.key != null
        ) {
          return child.key;
        }

        return crypto.randomUUID();
      }),
    [content],
  );

  /**
   * useIsomorphicLayoutEffect is used to measure the rendered content heights once the hidden container has been rendered.
   * On the server, useEffect is used instead of useLayoutEffect to avoid warnings.
   */
  useIsomorphicLayoutEffect(() => {
    if (!measurementRef.current) {
      return;
    }

    // Convert the measurement container's child nodes into an array of HTMLElements.
    const nodes = Array.from(measurementRef.current.childNodes) as HTMLElement[];
    // Map each node to a MeasuredItem, adding a fixed 16px for the gap between items.
    const measuredItems: MeasuredItem[] = nodes.map((node, index) => ({
      key: String(contentKeys[index]),
      content: content[index],
      height: node.getBoundingClientRect().height + 16, // 16px comes from the gap-4 between elements
    }));
    // Group the measured items into pages based on the contentHeight limit.
    const pagesArray: MeasuredItem[][] = [];
    let currentPage: MeasuredItem[] = [];
    let currentHeight = 0;
    // Helper function to push the current page to pagesArray and reset counters.
    const pushPage = () => {
      pagesArray.push(currentPage);
      currentPage = [];
      currentHeight = 0;
    };

    // Iterate over measured items and group them into pages.
    measuredItems.forEach((item) => {
      // If the item's height exceeds the page limit, place it on its own page.
      if (item.height > contentHeight) {
        if (currentPage.length > 0) {
          pushPage();
        }

        pagesArray.push([item]);
      } else {
        // Otherwise, try to add it to the current page.
        if (currentHeight + item.height <= contentHeight) {
          currentPage.push(item);
          currentHeight += item.height;
        } else {
          // If adding the item would exceed the limit, start a new page.
          pushPage();
          currentPage.push(item);
          currentHeight = item.height;
        }
      }
    });

    // If any items remain in the current page, push that page as well.
    if (currentPage.length > 0) {
      pagesArray.push(currentPage);
    }

    // Render each page as a div that contains the content for that page.
    const renderedPages = pagesArray.map((pageItems) => {
      // Create a composite key for the page from its items' keys.
      const pageKey = pageItems.map(item => item.key).join("-");
      // Check if the page contains a single item that exceeds the contentHeight (an "oversized" item).
      const isOversized = pageItems.length === 1 && pageItems[0].height > contentHeight;

      return (
        <div
          key={pageKey}
          className={cn(
            "relative mb-4 flex flex-col gap-4",
            // If not oversized, constrain the page height and hide overflow.
            !isOversized && `overflow-hidden h-[${contentHeight}px]`,
          )}
        >
          {pageItems.map(item => (
            <Fragment key={item.key}>{item.content}</Fragment>
          ))}
        </div>
      );
    });

    // Update the pages state with the newly rendered pages.
    setPages(renderedPages);
  }, [content, contentKeys, contentHeight]);

  /**
   * The measurement container renders all content items off-screen for height measurement.
   * Each content item is wrapped in a div with its stable key.
   */
  const measurementContainer = (
    <div
      ref={measurementRef}
      style={{ position: "absolute", top: -9999, left: -9999 }}
    >
      {content.map((child, index) => (
        <div key={contentKeys[index]}>{child}</div>
      ))}
    </div>
  );

  // Return the rendered pages and the measurement container.
  return { pages, measurementContainer };
}
