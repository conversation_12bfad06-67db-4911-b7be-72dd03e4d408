import { useEffect, useState } from "react";

/**
 * If we update isLoading instantly based on fetcher.state, it will cause a flicker effect.
 * This hook will delay the isLoading state change to prevent flickering and provide a better user experience.
 */
export function useDelayedState(currentState: boolean, delay: number): boolean {
  const [delayedState, setDelayedState] = useState<boolean>(currentState);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (currentState) {
      setDelayedState(true);
    } else {
      timer = setTimeout(() => {
        setDelayedState(false);
      }, delay);
    }

    return (): void => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [currentState, delay]);

  return delayedState;
}
