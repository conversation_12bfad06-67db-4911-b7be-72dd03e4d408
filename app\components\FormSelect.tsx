import type { ComponentProps, ReactNode } from "react";
import { Select, SelectContent, SelectTrigger, SelectValue } from "@netpro/design-system";
import { FormFieldReset } from "~/components/FormFieldReset";
import { makeFormField } from "~/lib/makeFormField";

type Props = {
  options: ReactNode[]
  selectProps?: ComponentProps<typeof Select>
  selectValueProps?: ComponentProps<typeof SelectValue>
}

export const FormSelect = makeFormField<Props>({ displayName: "FormSelect", render: ({ field, fieldState, selectProps, options, selectValueProps }) => {
  return (
    <div className="relative">
      <Select value={field.value} onValueChange={field.onChange} defaultValue={field.value} {...selectProps}>
        <SelectTrigger invalid={!!fieldState.error}>
          <SelectValue {...selectValueProps} />
        </SelectTrigger>
        <SelectContent>
          {options}
        </SelectContent>
      </Select>
      {/*
          This Should ideally be part of the <Select.Trigger><Select.Icon>...
          markup but that is not a supported API atm
      */}
      {field.value && !selectProps?.disabled && (
        <FormFieldReset variant="select" onReset={() => field.onChange(null)} />
      )}
    </div>
  )
} })
