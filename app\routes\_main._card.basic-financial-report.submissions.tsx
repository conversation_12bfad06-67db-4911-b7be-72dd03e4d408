import type { ReactNode } from "react";
import { Button, SelectItem } from "@netpro/design-system";
import { json, useLoaderData, useLocation, useNavigation, useSearchParams } from "@remix-run/react";
import { Download, Filter } from "lucide-react";
import queryString from "query-string";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormCombobox } from "~/components/FormCombobox";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { LinkButton } from "~/components/ui/buttons/LinkButton";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { useBfrColumns } from "~/features/basic-financial-report/hooks/use-bfr-columns";
import type { SearchSchemaType } from "~/features/basic-financial-report/schemas/searchSchema";
import { searchSchema } from "~/features/basic-financial-report/schemas/searchSchema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type { ListSubmissionDTOPaginatedResponse } from "~/services/api-generated";
import { managementPanamaListSubmissionsByModule } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Basic Financial Report",
    to: "/basic-financial-report/submissions",
  },
  title: "Submissions",
}

export const loader = makeEnhancedLoader(async ({ request, queryString }) => {
  await middleware(["auth"], request);
  const { module: bfrModule } = await requireActiveModule({ request, key: Modules.BASIC_FINANCIAL_REPORT });
  const schemaData = searchSchema.safeParse(queryString).data || {};
  if (!schemaData?.hideDeleted) {
    schemaData.hideDeleted = "true"; // Default to hiding deleted submissions
  }

  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });
  const isUsingAccountingRecordsTool = schemaData?.usingAccountingRecordsOnly === "true" ? true : schemaData?.usingAccountingRecordsOnly === "false" ? false : undefined
  const paginatedSubmissions = await managementPanamaListSubmissionsByModule({ headers: await authHeaders(request), query: {
    ModuleId: bfrModule.id,
    PageNumber: pageNumber,
    GeneralSearchTerm: schemaData?.search,
    SortOrder: orderDirection,
    SortBy: order as any,
    PageSize: pageSize,
    SubmittedAfterDate: schemaData?.submittedAfter,
    SubmittedBeforeDate: schemaData?.submittedBefore,
    FinancialPeriodStartAt: schemaData?.financialPeriodStart,
    FinancialPeriodEndAt: schemaData?.financialPeriodEnd,
    IsUsingAccountingRecordsTool: isUsingAccountingRecordsTool,
    IsDeleted: schemaData?.hideDeleted === "true" ? false : schemaData?.hideDeleted === "false" ? true : undefined,
  } }).then(res => res.data as ListSubmissionDTOPaginatedResponse);

  return json({
    paginatedSubmissions,
  });
}, { authorize: ["bfr.panama.submissions.view"] })

export default function BasicFinancialReportSubmissionsLayout(): ReactNode {
  const { paginatedSubmissions: { data: submissions, totalItemCount } } = useLoaderData<typeof loader>();
  const location = useLocation();
  const navigation = useNavigation();
  const { formMethods } = useFilterForm(searchSchema);
  const { columns: submissionColumns } = useBfrColumns();
  const usingAccountingRecordsOnlyOptions = [{ label: "All", value: "all" }, { label: "Yes", value: "true" }, { label: "No", value: "false" }]
  const [searchParams] = useSearchParams()
  const { columns, submittedAfter, submittedBefore, financialPeriodStart, financialPeriodEnd, search, usingAccountingRecordsOnly, hideDeleted } = Object.fromEntries(searchParams) as SearchSchemaType
  const queryParams = queryString.stringify({
    columns,
    submittedAfter,
    submittedBefore,
    financialPeriodStart,
    financialPeriodEnd,
    search,
    usingAccountingRecordsOnly,
    location: location.pathname,
    hideDeleted,
  }, { arrayFormat: "bracket" })

  return (
    <CardContainer>
      <Form formMethods={formMethods}>
        <FilterRow cols={4}>
          <FormDatePicker name="submittedAfter" label="Submitted After" />
          <FormDatePicker name="submittedBefore" label="Submitted Before" />
          <FormCombobox
            name="usingAccountingRecordsOnly"
            label="Using Accounting Records Only"
            options={usingAccountingRecordsOnlyOptions}
          />
          <FormColumnsFilter label="Visible Columns" columns={submissionColumns} />
          <FormDatePicker name="financialPeriodStart" label="Financial Period Start" />
          <FormDatePicker name="financialPeriodEnd" label="Financial Period End" />
          <FormSelect
            name="hideDeleted"
            label="Hide Deleted"
            selectValueProps={{ placeholder: "Yes", defaultValue: "true" }}
            options={["true", "false"].map((s: string) => (
              <SelectItem key={s} value={s}>{s === "true" ? "Yes" : "No"}</SelectItem>
            ))}
          />
          <div className="flex items-end">
            <LinkButton
              buttonProps={{
                type: "button",
                variant: "link",
                size: "sm",
                className: "gap-1.5",
              }}
              linkProps={{
                to: {
                  pathname: "/basic-financial-report/submissions/export",
                  search: queryParams,
                },
                reloadDocument: true,
              }}
            >
              Export as XLSX
              <Download className="size-4 mr-2" />
            </LinkButton>
          </div>
        </FilterRow>
        <FilterRow>
          <div className="col-span-full flex flex-row items-center gap-2">
            <FormSearch
              name="search"
              formItemProps={{ className: "w-full" }}
              inputProps={{ placeholder: "Search entity name, master client, referral office, etc." }}
            />
            <Button size="sm" className="gap-1.5" type="submit">
              <Filter size={14} />
              Apply Filter(s)
            </Button>
          </div>
        </FilterRow>
      </Form>
      <EnhancedTableContainer>
        <EnhancedTable
          sheetURL={row => `/basic-financial-report/submissions/${row.id}`}
          returnURL="/basic-financial-report/submissions"
          data={submissions}
          loading={<LoadingState isLoading={navigation.state === "loading"} />}
          rowId="id"
          columns={submissionColumns}
          totalItems={totalItemCount}
          defaultOpen={/^\/basic-financial-report\/submissions\/./.test(location.pathname)}
        />
      </EnhancedTableContainer>
    </CardContainer>
  );
}
