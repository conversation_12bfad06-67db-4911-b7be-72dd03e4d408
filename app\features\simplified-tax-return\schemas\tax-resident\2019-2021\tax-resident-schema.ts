import { z } from "zod";
import { stringBoolean } from "~/lib/utilities/zod-validators";

export const taxResidentSchema = z.object({
  incorporatedBefore2019: stringBoolean(),
  nonTaxResident: stringBoolean().optional(),
  residentCountry: z.string().optional(),
})
  .refine(data => !(data.incorporatedBefore2019 === "false" && !data.nonTaxResident), {
    message: "Required.",
    path: ["nonTaxResident"],
  })
  .refine(data => !(data.incorporatedBefore2019 === "false" && data.nonTaxResident === "false" && !data.residentCountry), {
    message: "Required.",
    path: ["residentCountry"],
  });

export type TaxResidentType = z.infer<typeof taxResidentSchema>;
