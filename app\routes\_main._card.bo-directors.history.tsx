import type { LoaderFunctionArgs } from "@remix-run/node";
import type { ReactNode } from "react";
import { middleware } from "~/lib/middlewares.server";

export const handle = {
  breadcrumb: {
    label: "History",
    to: "/bo-directors/history",
  },
  title: "History",
}

export async function loader({ request }: LoaderFunctionArgs): Promise<null | never> {
  await middleware(["auth"], request);

  return null;
}

export default function History(): ReactNode {
  return <div>BoDirectors History</div>;
}
