import { Button, cn } from "@netpro/design-system";
import { Link, Outlet, useLoaderData, useNavigate, useParams } from "@remix-run/react";
import { FileDown } from "lucide-react";
import { type ReactNode, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { ActionSheetActionRow } from "~/components/ActionSheetActionRow";
import { ActionSheetBody } from "~/components/ActionSheetBody";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetDescriptionList } from "~/components/ActionSheetDescriptionList";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { ActionSheetSection } from "~/components/ActionSheetSection";
import { Authorized } from "~/components/Authorized";
import { LinkButton } from "~/components/ui/buttons/LinkButton";
import { Pages } from "~/features/basic-financial-report/utilities/form-pages";
import { getUnflattenedDataSet } from "~/features/submissions/utilities/submission-data-set";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { fileDataToUrl } from "~/lib/utilities/files";
import { formatDate } from "~/lib/utilities/format";
import { getApiV1CommonDocumentsByDocumentId, managementGetSubmission } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, params, json, setNotification, redirect }) => {
  await middleware(["auth"], request);
  const { data: submission } = await managementGetSubmission({ headers: await authHeaders(request), path: { submissionId: params.id! }, query: { includeFormDocument: true } })

  if (!submission) {
    setNotification({ title: "The requested submission could not be found", variant: "error" })

    return redirect(`/basic-financial-report/submissions?${new URLSearchParams({ hideDeleted: "true" })}`)
  }

  const submissionData = getUnflattenedDataSet(submission);
  const documentId = submissionData[Pages.FINANCIAL_PERIOD]?.summaryReportDocumentId
  let summaryReport

  if (submission.documentIds?.length && documentId) {
    const { data: document } = await getApiV1CommonDocumentsByDocumentId({
      headers: await authHeaders(request),
      path: { documentId },
    });

    summaryReport = document
  }

  return json({ submission, summaryReport })
}, { authorize: ["bfr.panama.submissions.search"] });

export default function SubmissionDetail(): ReactNode {
  const params = useParams();
  const navigate = useNavigate();
  const formMethods = useForm();
  const { submission, summaryReport } = useLoaderData<typeof loader>();
  const [summaryReportUrl, setSummaryReportUrl] = useState<string | undefined>()

  function handleResetToSaved(): void {
    navigate(`/basic-financial-report/submissions/${params.id}/reset-to-saved`)
  }

  useEffect(() => {
    if (summaryReport) {
      const url = fileDataToUrl(summaryReport)
      setSummaryReportUrl(url)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <>
      <ActionSheetBody formMethods={formMethods}>
        <ActionSheetContent title="Submission Details">
          <ActionSheetSection title="Details" collapsible>
            <ActionSheetDescriptionList
              data={submission}
              headers={[
                ["legalEntityName", "Entity Name"],
                ["legalEntityCode", "VP Code"],
                ["masterClientCode", "Master Client Code"],
                ["status", "Status"],
                ["createdAt", "Created Date", ({ createdAt }): string => createdAt ? formatDate(createdAt, { timezone: "Panama", formatStr: "dd-MMM-yyyy" }) : ""],
                ["submittedAt", "Submitted Date", ({ submittedAt }): string => submittedAt ? formatDate(submittedAt, { timezone: "Panama", formatStr: "dd-MMM-yyyy" }) : ""],
                ["paymentReceivedAt", "Paid Date", ({ paymentReceivedAt }): string => paymentReceivedAt ? formatDate(paymentReceivedAt, { timezone: "Panama", formatStr: "dd-MMM-yyyy" }) : ""],
                ["financialYear", "Financial Year"],
                ["legalEntityReferralOffice", "Referral Office"],
              ]}
            />
          </ActionSheetSection>
          <Authorized oneOf={["bfr.panama.submissions.reset"]}>
            <ActionSheetSection title="Actions">
              <ActionSheetActionRow label="Reset Submission Status">
                <Button type="button" variant="destructive" size="sm" onClick={handleResetToSaved} disabled={submission.status !== "Submitted"}>
                  <span className="text-xs font-semibold">Reset to Saved</span>
                </Button>
              </ActionSheetActionRow>
            </ActionSheetSection>
          </Authorized>
          <ActionSheetSection title="Documents">
            <div className="flex-row space-y-2">
              <div>
                {summaryReportUrl
                  ? (
                      <LinkButton
                        linkProps={{ to: summaryReportUrl, className: cn("flex", !submission.submittedAt ? "pointer-events-none select-none" : ""), reloadDocument: true }}
                        buttonProps={{ className: "flex items-center", type: "button", size: "sm", disabled: !submission.submittedAt }}
                      >
                        <FileDown size={14} className="mr-2" />
                        <span className="text-xs font-semibold">Download Submission</span>
                      </LinkButton>
                    )
                  : (
                      <LinkButton
                        linkProps={{ to: `/basic-financial-report/${params.id}/summary`, className: cn("flex", !submission.submittedAt ? "pointer-events-none select-none" : ""), target: "_blank" }}
                        buttonProps={{ className: "flex items-center", type: "button", size: "sm", disabled: !submission.submittedAt }}
                      >
                        <FileDown size={14} className="mr-2" />
                        <span className="text-xs font-semibold">Download Submission</span>
                      </LinkButton>
                    )}
              </div>
              <div>
                <Link
                  to={`/invoices/${submission.invoiceId}/file`}
                  className={cn("flex", !submission.invoiceId ? "pointer-events-none select-none" : "")}
                  target="_blank"
                >
                  <Button className="flex items-center" type="button" size="sm" disabled={!submission.invoiceId}>
                    <FileDown size={14} className="mr-2" />
                    <span className="text-xs font-semibold">Download Invoice</span>
                  </Button>
                </Link>
              </div>
            </div>
          </ActionSheetSection>
        </ActionSheetContent>
        <ActionSheetFooter />
      </ActionSheetBody>
      <Outlet />
    </>
  )
}
