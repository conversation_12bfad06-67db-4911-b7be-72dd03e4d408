import type { CurrentStepActionKeys } from "../types/step";
import { createContext, useContext } from "react";

export type StepperContextType = {
  onChangeStep: (action: CurrentStepActionKeys) => void
};

export const StepperContext = createContext<StepperContextType | undefined>(undefined);

export function useStepperContext(): StepperContextType {
  const context = useContext(StepperContext);
  if (!context) {
    throw new Error("useStepperContext must be used within a StepperContextProvider");
  }

  return context;
}
