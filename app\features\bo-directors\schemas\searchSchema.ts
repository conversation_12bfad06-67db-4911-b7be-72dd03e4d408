import { z } from "zod";
import { optionalEnumWithEmpty } from "~/lib/optionalEnumWithEmpty";
import { optionalDateString } from "~/lib/utilities/zod-validators";

export const searchSchema = z.object({
  SearchTerm: z.string().optional(),
  columns: z.string().array().optional(),
  page: z.coerce.number().optional(),
  ConfirmedDateTo: optionalDateString,
  ConfirmedDateFrom: optionalDateString,
  Position: optionalEnumWithEmpty(["Director", "BeneficialOwner"]),
  ProductionOffice: optionalEnumWithEmpty(["TBVI", "THKO", "TNEV", "TPANVG", "TCYP"]),
  Specifics: z.enum(["NoBoDirInformation", "BoDirInformation", "MissingInformation"]).array().optional(),
  DataStatuses: z.enum(["Confirmed", "Initial", "PendingUpdateRequest", "Refreshed", "Subsequent"]).array().optional(),
});

export type SearchSchema = z.infer<typeof searchSchema>
