// eslint-disable-next-line no-restricted-imports
import { format } from "date-fns";
import { z } from "zod";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { Jurisdictions } from "~/lib/utilities/jurisdictions";
import { Modules } from "~/lib/utilities/modules";
import { managementExportSubmission } from "~/services/api-generated";

export const action = makeEnhancedLoader(async ({ request }) => {
  if (request.method !== "POST") {
    throw new Response("Method Not Allowed", { status: 405 });
  }

  await middleware(["auth"], request);
  const data = await request.json();
  const submissionIdType = z.object({
    submissionIds: z.array(z.string()).min(1),
    financialYear: z.coerce.number(),
  });
  const { submissionIds, financialYear } = submissionIdType.parse(data);
  const { data: fileData, error } = await managementExportSubmission({
    headers: await authHeaders(request),
    body: {
      module: Modules.SIMPLIFIED_TAX_RETURN,
      jurisdiction: Jurisdictions.NEVIS,
      financialYear,
      submissionIds,
    },
  });

  if (error) {
    throw new Error("Something went wrong downloading the export file.");
  }

  return new Response(fileData, {
    headers: {
      "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "Content-Disposition": `attachment; filename="submissions_${financialYear}_export_${format(new Date(), "yyyy_MM_dd_HH_mm_ss")}.xlsx"`,
    },
  });
}, { authorize: ["str.submissions.export.ird"] });
