import type { LoaderFunctionArgs } from "@remix-run/node";
import type { JSX } from "react";
import { middleware } from "~/lib/middlewares.server";

export const handle = {
  breadcrumb: {
    label: "Finance Companies",
    to: "/finance/companies",
  },
  title: "Annual Company Fees",
}

export async function loader({ request }: LoaderFunctionArgs): Promise<null | never> {
  await middleware(["auth"], request);

  return null;
}

export default function FinanceCompanies(): JSX.Element {
  return <div>FinanceCompanies</div>;
}
