import { companyFilingYearsSchemaServer } from "~/features/simplified-tax-return/schemas/companyFilingYearsSchema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { middleware } from "~/lib/middlewares.server";
import { Jurisdictions } from "~/lib/utilities/jurisdictions";
import { Modules } from "~/lib/utilities/modules";
import { getJurisdictionModules, getJurisdictions, managementGetPaidStatusByCompanyAndYear } from "~/services/api-generated";

export const action = makeEnhancedAction(async ({ json, request }) => {
  await middleware(["auth"], request);
  const formData = await request.formData()
  const bodyData = formData.get("data") as string
  //
  const { companyFilingYears } = companyFilingYearsSchemaServer.parse(JSON.parse(bodyData))
  //
  const bahamasId = await getJurisdictions({ headers: await authHeaders(request) }).then(({ data }) => data?.data?.find(({ code }) => code === Jurisdictions.BAHAMAS)?.id)

  if (!bahamasId) {
    throw new Response("Jurisdiction not found", { status: 404 })
  }

  const esId = await getJurisdictionModules({ headers: await authHeaders(request), path: { jurisdictionId: bahamasId } }).then(({ data }) => data?.modules?.find(({ key }) => key === Modules.ECONOMIC_SUBSTANCE_BAHAMAS)?.id)
  const { data, error } = await managementGetPaidStatusByCompanyAndYear({ headers: await authHeaders(request), body: { companyFilingYears, moduleId: esId } });
  if (error) {
    throw new Response(error.exceptionMessage as string, { status: 500 })
  }

  return json(data);
}, { authorize: ["es.bahamas.payments.import"] });
