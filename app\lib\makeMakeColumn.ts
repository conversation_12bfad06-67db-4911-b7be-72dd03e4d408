import type { AccessorColumnDef, ColumnDef, DisplayColumnDef, GroupColumnDef } from "@tanstack/react-table"

type Args<Columns, Data> = {
  id: Columns | (string & {})
} & (Omit<DisplayColumnDef<Data, any>, "id"> | Omit<GroupColumnDef<Data, any>, "id"> | AccessorColumnDef<Data, any>)

/**
 * Factory function that helps with automatically setting sortable columns to enableSorting: true, for
 * closer integration to the API also includes typings main advantage of this function is that if you follow
 * the example in app/routes/_main._card.simplified-tax-return.ird-export.tsx the sorting will/should be typesafe
 *
 *
 * @example
 * type SomeUnionType = 'Foo' | 'Bar' | 'Baz'
 *
 * // convert the union to a runtime "Enum"
 * const sortableColumnNames = Object.keys({
 * Foo: null,
 * Bar: null,
 * Baz: null,
 * } satisfies Record<SomeUnionType, null>)
 *
 * // create the helper
 * const makeColumn = makeMakeColumn<SortableColumns, SomeDTODefiningTheData>(sortableColumnNames)
 *
 * const columns: ColumnDef<SomeDTODefiningTheData>[] = [
 *  makeColumn({ id: "Foo", header: "Foo", accessorKey: "..." }),
 *  makeColumn({ id: "Bar", header: "Bar", accessorKey: "..." }),
 *  makeColumn({ id: "Baz", header: "Baz", accessorKey: "..." }),
 * ]
 */
export const makeMakeColumn = <SortableColumns extends string, Data>(sortableColumns: string[]) => (opts: Args<SortableColumns, Data>): ColumnDef<Data> => ({ enableSorting: sortableColumns.map(col => col.toLowerCase()).includes(opts.id.toLowerCase()), ...opts })
