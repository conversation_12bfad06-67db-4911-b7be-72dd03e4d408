trigger:
  - main
  - feat/*
  - fix/*

pool:
  vmImage: ubuntu-latest

jobs:
  - job: 'Lint'
    displayName: 'Code analysis (ESLint)'
    steps:
      - checkout: self
        clean: true
        persistCredentials: true
        displayName: 'Checkout repository'

      - task: NodeTool@0
        inputs:
          versionSource: 'fromFile'
          versionFilePath: './.nvmrc'
          checkLatest: true
        displayName: 'Install Node.js'

      - task: Npm@1
        inputs:
          command: 'install'
          workingDir: '.'
          verbose: true
        displayName: 'Install dependencies'

      - task: Npm@1
        inputs:
          command: 'custom'
          workingDir: '.'
          customCommand: 'run lint'
        displayName: 'Run ESLint'