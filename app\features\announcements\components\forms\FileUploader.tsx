import type { FileUploaderProps } from "@netpro/design-system";
import type { ReactNode } from "react";
import type { FileRejection } from "react-dropzone";
import { Dropzone, FileUploader as NetProFileUploader, notify } from "@netpro/design-system";
import { CloudUpload } from "lucide-react";

function handleRejections(rejections: FileRejection[]) {
  rejections.forEach((rejection) => {
    notify({
      title: "File Rejected",
      message: `${
        rejection.file.name
      } was rejected. Reason: ${rejection.errors[0].code}`,
      variant: "error",
      duration: 5000,
    });
  });
}

function handleFileChange(newFiles: File[], currentFiles: File[] | undefined, setFiles: (files: File[]) => void) {
  // Ensure currentFiles is defined, otherwise default to an empty array
  const filesList = currentFiles ?? [];
  // Filter out files with duplicate names and ensure no files with the same name are added
  const uniqueFiles = newFiles.filter(
    newFile => !filesList.some(currentFile => currentFile.name === newFile.name),
  );

  // Update the file list with only unique files if there are any
  if (uniqueFiles.length > 0) {
    setFiles([...filesList, ...uniqueFiles]);
  }
}

type Props = {
  children: ReactNode
  files: File[]
  setFiles: (files: File[]) => void
} & FileUploaderProps
export function FileUploader({ children, files, setFiles, ...props }: Props) {
  return (
    <NetProFileUploader
      {...props}
      setFiles={newFiles => handleFileChange(newFiles, files, setFiles)}
      onReject={handleRejections}
    >
      <Dropzone className="flex h-36 flex-col gap-2 border-gray-300">
        <CloudUpload className="size-10 text-primary" />
        {children}
      </Dropzone>
    </NetProFileUploader>
  )
}
