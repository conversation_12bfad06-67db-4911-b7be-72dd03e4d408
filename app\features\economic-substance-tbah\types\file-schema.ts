import { z } from "zod";

export const fileErrorMessage = "The file is required and should not be empty";
export const fileSchema = z.object({
  files: z.array(z.instanceof(File), { required_error: fileErrorMessage })
    .refine(value => value !== undefined && value.length > 0, {
      message: fileErrorMessage,
    }),
});

export type FileSchemaType = z.infer<typeof fileSchema>;

export function createFileSchema(options?: { optional?: boolean }) {
  return z.array(z.instanceof(File), { required_error: fileErrorMessage })
    .refine((value) => {
      // If files are optional, skip validation
      if (options?.optional) {
        return true;
      }

      // If files are required, ensure they are provided
      return value !== undefined && value.length > 0;
    }, {
      message: fileErrorMessage, // Error message when validation fails
    })
}
