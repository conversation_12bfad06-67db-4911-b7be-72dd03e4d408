import type {
  ActionFunctionArgs,
  LoaderFunctionArgs,
} from "@remix-run/node";
import type { SessionData as SessionDataValues } from "../types/session-type";
import { createCookieSessionStorage } from "@remix-run/node";

export const sessionStorage = createCookieSessionStorage({
  cookie: {
    name: "pcp-management-portal-session",
    secrets: [process.env.SESSION_SECRET || "pcp-management-portal-session-secret"],
    sameSite: "lax",
    path: "/",
    // 2 hours in seconds
    maxAge: 60 * 60 * 2,
    httpOnly: true,
    secure: process.env.NODE_ENV === "production", // only use https in production
  },
});

export const { commitSession, destroySession, getSession } = sessionStorage;

export async function getSessionData(request: ActionFunctionArgs["request"] | LoaderFunctionArgs["request"]): Promise<SessionDataValues> {
  const session = await getSession(request.headers.get("<PERSON>ie"));

  return session.data;
}
