import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@netpro/design-system";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeMakeColumn } from "~/lib/makeMakeColumn";
import type { ManagementListSubmissionsData, SubmissionDTO } from "~/services/api-generated";

type SortableColumns = NonNullable<NonNullable<ManagementListSubmissionsData["query"]>["SortBy"]>;

export const sortableColumnNames = Object.keys({
  Status: null,
  CreatedAt: null,
  ExportedAt: null,
  SubmittedAt: null,
  PaymentMethod: null,
  FinancialYear: null,
  LegalEntityCode: null,
  LegalEntityName: null,
  PaymentReference: null,
  MasterClientCode: null,
  PaymentReceivedAt: null,
  LegalEntityVPCode: null,
} satisfies Record<SortableColumns, null>)
const makeColumn = makeMakeColumn<SortableColumns, SubmissionDTO>(sortableColumnNames)

export function useExportSubmissionDataColumns() {
  const formatColDate = useFormatColDate()
  const exportSubmissionDataColumns: ColumnDef<SubmissionDTO>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected()
            || (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={value => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
    },
    makeColumn({
      id: "LegalEntityName",
      header: "Entity Name",
      accessorKey: "legalEntityName",
    }),
    makeColumn({
      id: "LegalEntityCode",
      header: "Entity Number",
      accessorKey: "legalEntityCode",
    }),
    makeColumn({
      id: "MasterClientCode",
      header: "Master Client Code",
      accessorKey: "masterClientCode",
    }),
    makeColumn({
      id: "Status",
      header: "Status",
      accessorKey: "status",
    }),
    makeColumn({
      id: "CreatedAt",
      header: "Date Created",
      accessorFn: formatColDate("createdAt", { timezone: "Panama" }),
    }),
    makeColumn({
      id: "SubmittedAt",
      header: "Date Submitted",
      accessorKey: "submittedAt",
      accessorFn: formatColDate("submittedAt", { timezone: "Panama" }),
    }),
    makeColumn({
      id: "PaymentMethod",
      header: "Payment Method",
      accessorKey: "paymentMethod",
    }),
  ]

  return exportSubmissionDataColumns
}
