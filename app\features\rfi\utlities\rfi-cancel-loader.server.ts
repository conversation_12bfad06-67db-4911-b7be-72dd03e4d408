import { SubmissionStatusNamesEnum } from "~/features/submissions/utilities/submission-status";
import type { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementGetSubmissionRfiDetails } from "~/services/api-generated";

type LoaderArgs = Parameters<Parameters<typeof makeEnhancedLoader>[0]>[0];

export async function getRfiLoader(route: string, {
  request,
  params,
}: LoaderArgs) {
  await middleware(["auth"], request);

  const { id } = params
  if (!id) {
    throw new Response("The id is required to get  the request for information details", { status: 400 })
  }

  // RFI Logic
  const { data: rfiData, error: rfiError } = await managementGetSubmissionRfiDetails({ path: { submissionId: id } })

  if (!rfiData) {
    throw new Response("Request for information data not found", { status: 404 })
  }

  const status = rfiData.status

  if (status !== SubmissionStatusNamesEnum.INFORMATION_REQUESTED) {
    throw new Response("Status should be request for information", { status: 403 })
  }

  const requestsForInformation = rfiData.requestsForInformation

  if (!requestsForInformation) {
    throw new Response("Request for information not found", { status: 404 })
  }

  if (rfiError) {
    throw new Response(rfiError, { status: 500 })
  }

  return null
}
