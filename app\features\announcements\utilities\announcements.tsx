import type { DisplayColumnDef } from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import type { ListAnnouncementDTO } from "~/services/api-generated";

export function useAnnouncementColumns() {
  const formatColDate = useFormatColDate();
  const columnHelper = createColumnHelper<ListAnnouncementDTO>();
  const announcementColumns: DisplayColumnDef<ListAnnouncementDTO>[] = [
    columnHelper.display({
      id: "sendAt",
      header: "Scheduled Date",
      enableSorting: true,
      cell: formatColDate("sendAt", { timezone: "UTC" }),
    }),
    columnHelper.display({
      id: "subject",
      header: "Subject",
      enableSorting: true,
      cell: props => props.row.original.subject,
    }),
    columnHelper.display({
      id: "masterClients",
      header: "Master Clients",
      enableSorting: true,
      cell: props => props.row.original.masterClientIds,
    }),
    columnHelper.display({
      id: "jurisdictions",
      header: "Jurisdiction",
      enableSorting: true,
      cell: props => props.row.original.jurisdictionIds,
    }),
    columnHelper.display({
      id: "status",
      header: "Status",
      enableSorting: true,
      cell: props => props.row.original.status,
    }),
  ];

  return { announcementColumns };
}
