import type { ModuleDTO } from "~/services/api-generated";
import { getModules } from "~/services/api-generated";
import { authHeaders } from "../auth/utils/auth-headers";

type GetActiveModuleParams = {
  key: string
  request: Request
}

export async function getActiveModule({ request, key }: GetActiveModuleParams): Promise<{
  module: ModuleDTO | undefined
}> {
  const { data } = await getModules({ headers: await authHeaders(request), query: {
    active: true,
  } });

  if (!data) {
    throw new Error("Modules not found");
  }

  const modules = data?.modules || [];

  return {
    module: modules.find(module => module.key === key && module.isActive),
  }
}

export async function requireActiveModule({ request, key }: GetActiveModuleParams): Promise<{
  module: ModuleDTO
}> {
  const { module } = await getActiveModule({ request, key });

  if (!module) {
    throw new Error("Module is not found or disabled");
  }

  return { module };
}
