import type { ComponentType } from "react";
import { cn } from "@netpro/design-system";
import React, { forwardRef } from "react";

export function withClassName<T extends keyof JSX.IntrinsicElements | ComponentType<any>>(
  Component: T,
  className: string | string[],
) {
  type Props = T extends keyof JSX.IntrinsicElements
    ? JSX.IntrinsicElements[T]
    : React.ComponentProps<T>;

  const WrappedComponent = forwardRef<HTMLElement, Props & { className?: string }>(
    ({ className: propClassName, ...restProps }, ref) => {
      return (
        <Component
          ref={ref}
          className={cn(className, propClassName)}
          {...(restProps as any)}
        />
      );
    },
  );

  if (typeof Component === "string") {
    WrappedComponent.displayName = `withClassName(${Component})`;
  } else if (Component.displayName) {
    WrappedComponent.displayName = Component.displayName;
  }

  return WrappedComponent;
}
