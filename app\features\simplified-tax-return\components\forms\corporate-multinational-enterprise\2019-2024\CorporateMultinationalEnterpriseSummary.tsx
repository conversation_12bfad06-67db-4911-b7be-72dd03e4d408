import type { ReactNode } from "react";
import { Table, TableBody, TableCell, TableRow } from "@netpro/design-system";
import type { CorporateMultinationalEnterpriseType } from "~/features/simplified-tax-return/schemas/corporate-multinational-enterprise/2019-2024/corporate-multinational-enterprise-schema";
import { Pages } from "~/features/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/features/submissions/context/use-submission";
import { formatYesNoBoolean } from "~/lib/utilities/format";

export function CorporateMultinationalEnterpriseSummary(): ReactNode {
  const { submissionData } = useSubmission();
  const corporateMNE = submissionData[Pages.CORPORATE_MULTINATIONAL_ENTERPRISE] as CorporateMultinationalEnterpriseType;

  return (
    <section id="corporate-mne-section">
      <Table className="border border-blue-600 pointer-events-none">
        <TableBody>
          <TableRow className="border border-t-0 border-blue-600">
            <TableCell className="w-2/3 py-1">
              <span>
                Is the corporation part of a Multinational Enterprise (MNE) group with annual
                consolidated group revenue of €750 million (or XCD equivalent) in the immediately
                preceding fiscal year?
              </span>
            </TableCell>
            <TableCell className="w-1/3 text-center py-1">
              <span className="font-semibold ">{formatYesNoBoolean(corporateMNE?.isPartOfMNEGroup)}</span>
            </TableCell>
          </TableRow>
          <TableRow className="border border-blue-600">
            <TableCell className="w-2/3 py-1 ">
              <span>
                Is the corporation&#x2019;s data required to be reported as part of a Country-by-Country report to a tax authority of a jurisdiction outside the Federation?
              </span>
            </TableCell>
            <TableCell className="w-1/3 text-center py-1">
              <span className="font-semibold ">{formatYesNoBoolean(corporateMNE?.requiresCbCReport) ?? "N/A"}</span>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </section>
  );
}
