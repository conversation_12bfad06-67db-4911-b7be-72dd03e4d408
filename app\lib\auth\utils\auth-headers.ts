import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import type { SessionData } from "../types/session-type";
import { getClientCredentialsToken } from "./authentication.server";
import { getSession } from "./session.server";

export async function authHeaders(request: ActionFunctionArgs["request"] | LoaderFunctionArgs["request"]) {
  const session = await getSession(request.headers.get("Cookie"));
  const { userId } = session.data as SessionData;
  const { accessToken } = await getClientCredentialsToken();

  return {
    "Authorization": `Bearer ${accessToken}`,
    "x-userid": userId,
  };
}
