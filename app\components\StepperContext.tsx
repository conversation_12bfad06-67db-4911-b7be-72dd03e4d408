import type { ReactNode } from "react";
import { useCallback, useEffect, useMemo } from "react";
import { StepperContext } from "~/lib/hooks/useStepperContext";
import type { CurrentStepActionKeys } from "~/lib/types/step";

type StepperContextProviderProps = {
  children: ReactNode
  setCurrentStep: React.Dispatch<React.SetStateAction<number>>
  currentStep: number
};

export function StepperContextProvider({ children, setCurrentStep, currentStep }: StepperContextProviderProps): ReactNode {
  const handleChangeStep = useCallback((action: CurrentStepActionKeys): void => {
    setCurrentStep(prev => prev + action);
  }, [setCurrentStep])
  const value = useMemo(() => ({
    onChangeStep: handleChangeStep,
  }), [handleChangeStep])

  useEffect(() => {
    setCurrentStep(currentStep);
  }, [currentStep, setCurrentStep]);

  return <StepperContext.Provider value={value}>{children}</StepperContext.Provider>;
}
