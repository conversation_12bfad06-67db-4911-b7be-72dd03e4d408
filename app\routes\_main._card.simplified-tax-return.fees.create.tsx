import type { ReactNode } from "react";
import type { FeesLoaderData } from "./_main._card.simplified-tax-return.fees";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@netpro/design-system";
import { useParams, useRouteLoaderData } from "@remix-run/react";
// eslint-disable-next-line no-restricted-imports
import { format } from "date-fns";
import { useForm } from "react-hook-form";
import { ActionSheetBody } from "~/components/ActionSheetBody";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { FormInput } from "~/components/FormInput";
import { strFeeSchema, type StrFeeSchemaType } from "~/features/fees/schemas/str-fee";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { Jurisdictions } from "~/lib/utilities/jurisdictions";
import { requireActiveJurisdiction } from "~/lib/utilities/require-active-jurisdiction";
import type { SettingsDTO } from "~/services/api-generated";
import { setJurisdictionSettings } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request }) => {
  await middleware(["auth"], request);

  return null;
}, { authorize: ["str.fee.set"] });

export const action = makeEnhancedAction(async ({ request, setNotification, redirect }) => {
  await middleware(["auth"], request);
  const { jurisdiction: strJurisdiction } = await requireActiveJurisdiction({ request, code: Jurisdictions.NEVIS });
  const formData = await request.formData()
  const fee = Number(formData.get("fee"));
  const currentDate = format(new Date(), "yyyy-MM-dd");
  const body: SettingsDTO = {
    feeSettings: { strSubmissionFee: fee, strSubmissionFeeInvoiceText: currentDate },
  }
  const { error } = await setJurisdictionSettings({ headers: await authHeaders(request), path: { jurisdictionId: strJurisdiction.id || "" }, body })
  if (error) {
    setNotification({ title: "Failed to create the STR fee", variant: "error" })
  } else {
    setNotification({ title: "Create STR fee successfully", variant: "success" })
  }

  return redirect("/simplified-tax-return/fees")
}, { authorize: ["str.fee.set"] });

export default function StrFeeCreation(): ReactNode {
  const params = useParams();
  const feeMgmtLoaderData = useRouteLoaderData<FeesLoaderData>("routes/_main._card.simplified-tax-return.fees")
  if (!feeMgmtLoaderData) {
    throw new Error("The fees loader data is required to retrieve data")
  }

  const formMethods = useForm<StrFeeSchemaType>({
    defaultValues: {
      fee: !params.id || Number.isNaN(Number.parseFloat(params.id)) ? 0 : Number.parseFloat(params.id),
    },
    resolver: zodResolver(strFeeSchema),
  });

  return (
    <ActionSheetBody formMethods={formMethods}>
      <ActionSheetContent title="Update STR Fee">
        <span className="text-bold text-xl text-blue-700">Settings</span>
        <span className="text-sm">STR submission fee *</span>
        <FormInput
          name="fee"
          formItemProps={{ className: "w-full" }}
          inputProps={{ placeholder: "0.00", type: "number", min: 0, ...formMethods.register("fee") }}
        />
        <span className="text-sm text-gray-500">Note: This is the regular amount that is charged to all submissions for all years, unless they have a deviation on the company level</span>
      </ActionSheetContent>
      <ActionSheetFooter>
        <Button size="sm" type="submit">Save</Button>
      </ActionSheetFooter>
    </ActionSheetBody>
  )
}
