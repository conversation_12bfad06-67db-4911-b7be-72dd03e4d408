import { useLoaderData } from "@remix-run/react";
import type { ExpenditureSchemaType } from "~/features/economic-substance-tbah/types/expenditure-schema";
import type { IncomeSchemaType } from "~/features/economic-substance-tbah/types/income-schema";
import { getCurrencyString } from "~/features/economic-substance-tbah/utilities/currencies";
import type { PageSlug } from "~/features/economic-substance-tbah/utilities/form-pages";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";
import { SummaryTable } from "../table/SummaryTable";
import { SummaryTableData } from "../table/SummaryTableData";
import { SummaryTableRow } from "../table/SummaryTableRow";

export function IncomeExpenditure({ page }: { page: PageSlug }) {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { totalGrossIncome, netBookValuesAssets, assetsDescriptionBahamas } = submissionData[page] as IncomeSchemaType
  const { totalExpenditureBahamas, totalExpenditureRelevantActivity } = submissionData[page] as ExpenditureSchemaType

  return (
    <div>
      <h2 className="text-blue-500 font-thin mb-4 text-lg">1. Income and Expenditure</h2>
      <SummaryTable>
        <tbody>
          <SummaryTableRow>
            <SummaryTableData>
              Total Gross Income for the relevant activity during the financial period.
            </SummaryTableData>
            <SummaryTableData>{getCurrencyString(totalGrossIncome)}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Net book values of tangible assets, equipment or physical assets held in the
              course of carrying out the relevant activity.
            </SummaryTableData>
            <SummaryTableData>{getCurrencyString(netBookValuesAssets)}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Description of nature of any equipment and other tangible or physical
              assets located within the Bahamas used in connection with the relevant
              activity.
            </SummaryTableData>
            <SummaryTableData>{assetsDescriptionBahamas}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Total expenditure incurred in the operations of the relevant activity during
              the financial period (including outsourcing, if applicable)
            </SummaryTableData>
            <SummaryTableData>{getCurrencyString(totalExpenditureBahamas)}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Total expenditure incurred in the Bahamas in the operations of the relevant
              activity during the financial period (including outsourcing, if applicable)
            </SummaryTableData>
            <SummaryTableData>{getCurrencyString(totalExpenditureRelevantActivity)}</SummaryTableData>
          </SummaryTableRow>
        </tbody>
      </SummaryTable>
    </div>
  )
}
