import { searchSchema } from "~/features/bo-directors/schemas/searchSchema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { managementDownloadBoDirList } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, queryString }) => {
  const searchParseResult = searchSchema.safeParse(queryString)
  const downloadListResponse = await managementDownloadBoDirList({ headers: await authHeaders(request), query: searchParseResult.data })

  return new Response(downloadListResponse.data, {
    status: 200,
    headers: downloadListResponse.response.headers,
  })
}, { authorize: ["bo-dir.export"] })
