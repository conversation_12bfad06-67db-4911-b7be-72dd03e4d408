import type { ReactNode } from "react";
import { Outlet } from "@remix-run/react";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";

export const handle = {
  breadcrumb: {
    label: "Companies",
    to: "/companies",
  },
  title: "View History Logs",
  /*
   * This is the path to return to when the back button is clicked
   * It's required to define this path to return to the correct page
   */
  returnTo: {
    to: (id: string) => `/companies/overview/${id}/edit`,
    label: "Back to Company details",
  },
}

export const loader = makeEnhancedLoader(async ({ request }) => {
  await middleware(["auth"], request);

  return null;
}, {
  authorize: ["companies.log.view"],
});

export default function Layout(): ReactNode {
  return <Outlet />
}
