import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import type { PageRange, PaginationParams } from "~/lib/hooks/usePaginationParams";
import { PAGINATION } from "~/lib/hooks/usePaginationParams";
import { getUserPreferences } from "~/lib/utilities/get-user-preferences";

type Props = {
  request: LoaderFunctionArgs["request"] | ActionFunctionArgs["request"]
}

/**
 * Get pagination parameters from the request object in an action or loader function.
 */
export async function getPaginationParams({ request }: Props): Promise<PaginationParams> {
  const url = new URL(request.url);
  const preferences = await getUserPreferences({ request })

  return {
    pageNumber: Number(url.searchParams.get("page") ?? PAGINATION.PAGE_NUMBER),
    pageSize: (preferences.tablePageSize ?? PAGINATION.PAGE_SIZE) as PageRange,
    order: url.searchParams.get("order") ?? undefined,
    orderDirection: url.searchParams.get("orderDirection") ?? undefined,
  };
}
