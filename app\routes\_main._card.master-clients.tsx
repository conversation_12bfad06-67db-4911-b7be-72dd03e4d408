import type { ReactNode } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { Outlet, useLoaderData, useLocation, useNavigate, useNavigation, useSearchParams } from "@remix-run/react";
import { Filter } from "lucide-react";
import { z } from "zod";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormSearch } from "~/components/FormSearch";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { Pagination } from "~/components/ui/filters/Pagination";
import { PageMessage } from "~/components/ui/PageMessage";
import { MasterClientListItem } from "~/features/master-clients/MasterClientListItem";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { getMasterClients } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Master Clients",
    to: "/master-clients",
  },
  title: "Overview",
};

const searchSchema = z.object({
  search: z.string().optional(),
})

export const loader = makeEnhancedLoader(async ({ request, queryString, json }) => {
  await middleware(["auth"], request);
  const schemaData = searchSchema.safeParse(queryString).data;
  const { pageNumber, pageSize } = await getFilterParams({ request });
  const { data: paginatedMasterClients, error } = await getMasterClients({ headers: await authHeaders(request), query: {
    pageNumber,
    pageSize,
    searchTerm: schemaData?.search,
  } });

  if (error) {
    // Unhandled API error
    console.error("Error fetching Master Clients", error);
    throw new Response("Currently unable to retrieve Master Clients", { status: 412 });
  }

  return json({
    paginatedMasterClients,
  });
}, { authorize: ["masterclients.search"] })

export default function MasterClientsComponent(): ReactNode {
  const [searchParams] = useSearchParams();
  const { paginatedMasterClients: { data: masterClients, totalItemCount } } = useLoaderData<typeof loader>();
  const searchTerm = searchParams.get("search") ?? false;
  const navigate = useNavigate();
  const location = useLocation();
  const navigation = useNavigation();
  const { formMethods } = useFilterForm(searchSchema);
  let message = "Unable to load Master Client data. Please try again later.";
  if (searchTerm) {
    message = `No master clients found for search term "${searchTerm}".`;
  }

  const handleRowClick = (id: string) => {
    const searchParams = new URLSearchParams(location.search);
    const currentSearch = searchParams.toString();
    navigate(`/master-clients/${id}/edit?${currentSearch}`);
  }

  return (
    <CardContainer>
      <Form formMethods={formMethods}>
        <FilterRow>
          <div className="col-span-full flex flex-row items-center gap-2">
            <FormSearch name="search" formItemProps={{ className: "w-full" }} inputProps={{ placeholder: "Search master client code or owner/manager email address, etc." }} />
            <Button size="sm" className="gap-1.5" type="submit">
              <Filter size={14} />
              Apply Filter(s)
            </Button>
          </div>
        </FilterRow>
      </Form>

      <EnhancedTableContainer>
        {navigation.state === "loading"
          ? (
              <div className="flex flex-col items-center justify-center h-96 gap-6 max-w-3xl mx-auto">
                <div className="flex flex-col items-center">
                  <LoadingState isLoading className="relative" />
                </div>
              </div>
            )
          : (
              <>
                {totalItemCount === 0 && (
                  <PageMessage
                    title="No master clients found"
                    subtitle={message}
                  />
                )}
                {masterClients && masterClients.length > 0 && (
                  <>
                    {masterClients.map(client => (
                      <MasterClientListItem
                        key={client.id}
                        client={client}
                        onClick={() => handleRowClick(client.id!)}
                      />
                    ))}

                    <Pagination totalItems={totalItemCount as number} />
                  </>
                )}
              </>
            )}
      </EnhancedTableContainer>

      <Outlet />
    </CardContainer>
  );
}

export const ErrorBoundary = PageErrorBoundary;
