import type { LoaderFunctionArgs } from "@remix-run/node";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { middleware } from "~/lib/middlewares.server";
import { removeUserFromMasterClient } from "~/services/api-generated";

export async function loader({ request }: LoaderFunctionArgs): Promise<null | never> {
  await middleware(["auth"], request);

  return null;
}

export const action = makeEnhancedAction(async ({ json, params, request }) => {
  await middleware(["auth"], request);

  const result = await removeUserFromMasterClient({ headers: await authHeaders(request), path: {
    masterclientId: params.id as string,
    userId: params.managerId as string,
  } })

  /*
   * TODO: Check with API team why the error object is not an instance of ProblemDetails
   * Nov 20, 2024; Asked <PERSON>, awaiting response.
   */
  if ("error" in result && result.error) {
    return json({
      errors: {
        managerEmail: result.error.exceptionMessage as string,
      },
    })
  }

  return json({
    success: true,
  })
  // TODO: remove doesn't seem to be a permission and masterclients.trident-users.add may not suffice
}, { authorize: ["masterclients.trident-users.add"] })
