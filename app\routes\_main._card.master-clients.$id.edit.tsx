import type { ActionFunctionArgs, TypedResponse } from "@remix-run/node";
import type { ReactNode } from "react";
import { <PERSON><PERSON>, ScrollArea, Separator, Sheet, SheetContent, SheetPortal } from "@netpro/design-system";
import { json, useLoaderData, useLocation } from "@remix-run/react";
import { useEffect, useState } from "react";
import { Authorized } from "~/components/Authorized";
import { OverlayErrorBoundary } from "~/components/errors/OverlayErrorBoundary";
import { Accordion } from "~/components/ui/accordion";
import { AccordionMasterClientManagers } from "~/features/master-clients/AccordionMasterClientManagers";
import { AccordionMasterClientUsers } from "~/features/master-clients/AccordionMasterClientUsers";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useDelayedNavigate } from "~/lib/hooks/useDelayedNavigate";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import type { ListUserDTO } from "~/services/api-generated";
import { getMasterClient } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, params, json }) => {
  await middleware(["auth"], request);

  const { id: masterClientId } = params;
  const { data: masterClient, error } = await getMasterClient({ headers: await authHeaders(request), path: {
    masterclientId: masterClientId as string,
  } });

  if (error) {
    throw new Response("Master Client not found", { status: 412 });
  }

  return json({ masterClient })
}, { authorize: ["masterclients.view"] });

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse | never> {
  await middleware(["auth"], request);

  return json({
    userInvite: {
      success: true,
      error: null,
    },
  });
}

export default function EditMasterClientRoute(): ReactNode {
  const [sheetContainer, setSheetContainer] = useState<Element | null>(null);
  const { masterClient } = useLoaderData<typeof loader>();
  const [open, setOpen] = useState(true);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const currentSearch = searchParams.toString();
  const delayedNavigate = useDelayedNavigate({
    beforeDelay() {
      setOpen(false);
    },
  });

  useEffect(() => {
    // This is a workaround to prevent hydration errors.
    setSheetContainer(document.body);
  }, []);

  return (
    <Sheet open={open} onOpenChange={open => !open && delayedNavigate(`/master-clients?${currentSearch}`)}>
      <SheetPortal container={sheetContainer}>
        <SheetContent side="right" className="[&>button]:hidden" onPointerDownOutside={e => e.preventDefault()}>
          <div className="h-full flex flex-col">
            <div className="flex-none mb-2">
              <h2 className="text-2xl font-normal">
                {`Master Client ${masterClient?.code || "Undefined"}`}
              </h2>
              <Separator className="my-4" />
            </div>
            <ScrollArea className="h-full">

              <Accordion type="multiple" defaultValue={["mccManagers"]} className="w-full">
                <Authorized oneOf={["masterclients.view"]}>
                  <AccordionMasterClientUsers value="mccUsers" users={masterClient.masterClientUsers as ListUserDTO[]} />
                </Authorized>
                <Authorized oneOf={["masterclients.trident-users.view", "masterclients.trident-users.add"]}>
                  <AccordionMasterClientManagers value="mccManagers" managers={masterClient.masterClientManagers as ListUserDTO[]} />
                </Authorized>
              </Accordion>

            </ScrollArea>
            <div className="flex-none mt-auto">
              <Separator className="my-4" />
              <div className="flex justify-end">
                <Button size="sm" variant="outline" onClick={() => delayedNavigate(`/master-clients?${currentSearch}`)}>
                  Close
                </Button>
              </div>
            </div>
          </div>
        </SheetContent>
      </SheetPortal>
    </Sheet>
  );
}

export const ErrorBoundary = OverlayErrorBoundary;
