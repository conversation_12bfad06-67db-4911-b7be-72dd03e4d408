import type { CurrentAssetsDetailsSchemaType } from "../types/current-assets-details-schema";
import type { EquityDetailsSchemaType } from "../types/equity-details-schema";
import type { ExpenseDetailsSchemaType } from "../types/expense-details-schema";
import type { IncomeDetailsSchemaType } from "../types/income-details-schema";
import type { LiabilitiesDetailsSchemaType } from "../types/liabilities-details-schema";
import type { NonCurrentAssetsDetailsSchemaType } from "../types/non-current-assets-details-schema";
import { sumArray, toNumber } from "../utilities/calculation";
import { Pages } from "../utilities/form-pages";
import { useProfitLossCalculation } from "./use-profit-loss-calculation";

export function useBalanceSheetCalculation(submissionData: Record<string, any>) {
  const { netIncomeLoss } = useProfitLossCalculation(submissionData)
  const { properties, securitiesValue, noIssuedShares, parValuePerShare, cashAmount } = submissionData[Pages.EQUITY_DETAILS] as EquityDetailsSchemaType;
  const { totalPurchaseCost, totalMarketValue, endingInterestReceivable, otherEndingInterestReceivable, beginningInterestReceivable, otherBeginningInterestReceivable } = submissionData[Pages.INCOME_DETAILS] as IncomeDetailsSchemaType;
  const { portfolioManagementFees, companyAdministrationFees, otherCompanyExpenses, beginningInterestPayable, otherPeriodPaidExpenses, endingInterestPayableLoans, otherPeriodNotPaidExpenses, dividendsNotPaidShareholders } = submissionData[Pages.EXPENSE_DETAILS] as ExpenseDetailsSchemaType;
  const { loans: loansForm5, accountPayableAccrual: accountsPayableAccrualForm5, otherLiabilities: otherLiabilitiesForm5 } = submissionData[Pages.LIABILITIES_DETAILS] as LiabilitiesDetailsSchemaType;
  const { fixedAssets: fixedAssetsForm2, amountPaid } = submissionData[Pages.NON_CURRENT_ASSETS_DETAILS] as NonCurrentAssetsDetailsSchemaType;
  const { cashBankAccounts } = submissionData[Pages.CURRENT_ASSETS_DETAILS] as CurrentAssetsDetailsSchemaType;
  // Intangible assets calculation
  const intangibleAssets = 0;
  // Fixed assets calculation
  const totalPropertiesValue = properties ? sumArray(properties, p => toNumber(p.value)) : 0;
  const totalFixedAssetsAssessedValue = fixedAssetsForm2 ? sumArray(fixedAssetsForm2, f => toNumber(f.assessedValue)) : 0;
  const fixedAssets = totalPropertiesValue + totalFixedAssetsAssessedValue;
  // Total Fixed Assets calculation
  const totalFixedAssets = fixedAssets;
  // Investments calculation
  const securitiesProcessedValue = toNumber(securitiesValue);
  const totalPurchaseCostProcessedValue = toNumber(totalPurchaseCost);
  const totalMarketValueProcessedValue = toNumber(totalMarketValue);
  const investments = securitiesProcessedValue + totalPurchaseCostProcessedValue + (totalMarketValueProcessedValue - securitiesProcessedValue - totalPurchaseCostProcessedValue);
  // Bank / cash balances calculation
  const totalCashBanksAccount = cashBankAccounts ? sumArray(cashBankAccounts, c => toNumber(c.amount)) : 0;
  const bankCashBalances = totalCashBanksAccount;
  // Other current assets calculation
  const totalOtherEndingInterestReceivable = otherEndingInterestReceivable ? sumArray(otherEndingInterestReceivable, o => toNumber(o.amount)) : 0;
  const totalOtherBeginningInterestReceivable = otherBeginningInterestReceivable ? sumArray(otherBeginningInterestReceivable, o => toNumber(o.amount)) : 0;
  const otherCurrentAssets = toNumber(endingInterestReceivable) + totalOtherEndingInterestReceivable + toNumber(beginningInterestReceivable) + totalOtherBeginningInterestReceivable;
  // Total current assets calculation
  const totalCurrentAssets = investments + bankCashBalances + otherCurrentAssets;
  // Loans due within one year calculation
  const totalLoansDueWithinOneYear = loansForm5 ? sumArray(loansForm5, l => toNumber(l.current)) : 0;
  const loansDueWithinOneYear = totalLoansDueWithinOneYear;
  // Due To/(From) shareholder calculation
  const processedNoIssuedShares = toNumber(noIssuedShares);
  const processedParValuePerShare = toNumber(parValuePerShare);
  const processedCashAmount = toNumber(cashAmount);
  const processedPortfolioManagementFees = toNumber(portfolioManagementFees);
  const processedCompanyAdministrationFees = toNumber(companyAdministrationFees);
  const processedOtherCompanyExpenses = otherCompanyExpenses ? sumArray(otherCompanyExpenses, o => toNumber(o.amount)) : 0;
  const sumPage2 = processedCashAmount + totalPropertiesValue + securitiesProcessedValue;
  const productPage2 = processedNoIssuedShares + processedParValuePerShare;
  let dueToFromShareholder: number = 0;
  if (sumPage2 === 0) {
    dueToFromShareholder = -productPage2;
  } else if (productPage2 < sumPage2) {
    dueToFromShareholder = 0;
  } else {
    dueToFromShareholder = -(productPage2 - sumPage2);
  }

  dueToFromShareholder += processedPortfolioManagementFees + processedCompanyAdministrationFees + processedOtherCompanyExpenses;

  // Accounts payable and accruals calculation
  const processedBeginningInterestPayable = toNumber(beginningInterestPayable);
  const totalOtherPeriodPaidExpenses = otherPeriodPaidExpenses ? sumArray(otherPeriodPaidExpenses, o => toNumber(o.amount)) : 0;
  const processedEndingInterestPayableLoans = toNumber(endingInterestPayableLoans);
  const totalOtherPeriodNotPaidExpenses = otherPeriodNotPaidExpenses ? sumArray(otherPeriodNotPaidExpenses, o => toNumber(o.amount)) : 0;
  const processedAmountPaid = toNumber(amountPaid);
  const totalFixedAssetsPurchaseCost = fixedAssetsForm2 ? sumArray(fixedAssetsForm2, f => toNumber(f.purchaseCost)) : 0;
  const totalAccountsPayableAccrual = accountsPayableAccrualForm5 ? sumArray(accountsPayableAccrualForm5, a => toNumber(a.current)) : 0;
  const conditionalResult = processedAmountPaid < totalFixedAssetsPurchaseCost ? totalFixedAssetsPurchaseCost - processedAmountPaid : 0;
  const accountPayableAccrual = -processedBeginningInterestPayable - totalOtherPeriodPaidExpenses + processedEndingInterestPayableLoans
    + totalOtherPeriodNotPaidExpenses + conditionalResult + totalAccountsPayableAccrual;
  // Other liabilities due within one year calculation calculation
  const totalOtherLiabilitiesDueWithinOneYear = otherLiabilitiesForm5 ? sumArray(otherLiabilitiesForm5, ol => toNumber(ol.current)) : 0
  const processedDividendsNotPaidShareholders = toNumber(dividendsNotPaidShareholders)
  const otherLiabilitiesDueWithinOneYear = totalOtherLiabilitiesDueWithinOneYear + processedDividendsNotPaidShareholders
  // Total current liabilities calculation
  const totalCurrentLiabilities = loansDueWithinOneYear + dueToFromShareholder + accountPayableAccrual + otherLiabilitiesDueWithinOneYear
  // Total assets les current liabilities calculation
  const totalAssetsLessCurrentLiabilities = totalFixedAssets + totalCurrentAssets - totalCurrentLiabilities
  // Loans due after more than one year calculation
  const totalLoansDueAfterOneYear = loansForm5 ? sumArray(loansForm5, l => toNumber(l.nonCurrent)) : 0;
  const loansDueAfterOneYear = totalLoansDueAfterOneYear
  // Other liabilities more than one year calculation
  const totalOtherLiabilitiesDueAfterOneYear = otherLiabilitiesForm5 ? sumArray(otherLiabilitiesForm5, ol => toNumber(ol.nonCurrent)) : 0
  const otherLiabilitiesDueAfterOneYear = totalOtherLiabilitiesDueAfterOneYear
  // Total Non-Current Liabilities calculation
  const totalNonCurrentLiabilities = loansDueAfterOneYear + otherLiabilitiesDueAfterOneYear
  // Net assets calculation
  const netAssets = totalAssetsLessCurrentLiabilities - totalNonCurrentLiabilities
  // Share capital calculation
  const shareCapital = processedNoIssuedShares + processedParValuePerShare
  // Profit and loss account calculation
  const profitAndLossAccount = netIncomeLoss
  // Other shareholder reserves calculation
  const sharesProduct = processedNoIssuedShares * processedParValuePerShare
  const totalInitialCapitalInvestment = processedAmountPaid + totalPropertiesValue + securitiesProcessedValue
  const otherShareholderReserves = sharesProduct < totalInitialCapitalInvestment ? (totalInitialCapitalInvestment - sharesProduct) : 0
  const totalEquity = shareCapital + profitAndLossAccount + otherShareholderReserves

  return {
    intangibleAssets,
    fixedAssets,
    totalFixedAssets,
    investments,
    bankCashBalances,
    otherCurrentAssets,
    totalCurrentAssets,
    loansDueWithinOneYear,
    dueToFromShareholder,
    accountPayableAccrual,
    otherLiabilitiesDueWithinOneYear,
    totalCurrentLiabilities,
    totalAssetsLessCurrentLiabilities,
    loansDueAfterOneYear,
    otherLiabilitiesDueAfterOneYear,
    totalNonCurrentLiabilities,
    netAssets,
    shareCapital,
    profitAndLossAccount,
    otherShareholderReserves,
    totalEquity,
  };
}
