import { useCallback } from "react";
import type { UnprocessedRecordDTO } from "~/services/api-generated";

export function useMigrationErrors(unprocessedRecords: UnprocessedRecordDTO[]) {
  return useCallback(
    (entityName: string | null | undefined) => {
      if (!entityName) {
        return [];
      }

      return unprocessedRecords.filter(record => record.entityType === entityName);
    },
    [unprocessedRecords],
  );
}
