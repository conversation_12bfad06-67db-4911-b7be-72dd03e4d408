import { addMonths, isBefore } from "date-fns";
import { z } from "zod";
import { nonNullDate, stringBoolean } from "~/lib/utilities/zod-validators";

export const financialPeriodSchema = z.object({
  firstFinancialReport: stringBoolean(),
  startDate: nonNullDate("Start date"),
  endDate: nonNullDate("End date"),
  isReclassifiedToPEH: stringBoolean(),
}).refine(
  (data) => {
    if (!data.startDate || !data.endDate) {
      return true; // Skip if dates are not set
    }

    const maxEndDate = addMonths(data.startDate, 12);

    return isBefore(data.endDate, maxEndDate);
  },
  {
    message: "The Financial period end date must be within 12 months after the Financial period start date",
    path: ["endDate"],
  },
);

export type FinancialPeriodSchemaType = z.infer<typeof financialPeriodSchema>;
