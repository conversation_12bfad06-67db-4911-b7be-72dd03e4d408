import { DEFAULT_FORMAT, formatDate, type Timezone } from "~/lib/utilities/format";

type Options = {
  fallback?: string
  timezone?: Timezone
  formatStr?: string
}

export function useFormatColDate() {
  return function formatColDate<Row extends object>(key: keyof Row, options?: Options) {
    const { fallback = "", timezone = "UTC", formatStr = DEFAULT_FORMAT } = options || {}

    return function formatter(input: Row | { row: { original: Row } }) {
      const data = "row" in input ? input.row.original : input;

      return data[key] && typeof data[key] === "string"
        ? formatDate(data[key], { timezone, formatStr, fallbackMessage: fallback })
        : fallback
    }
  }
}
