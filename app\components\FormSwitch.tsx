import type { SwitchProps } from "@netpro/design-system";
import type { ComponentPropsWithoutRef, FC } from "react";
import { FormControl, FormField, FormItem, FormLabel, FormMessage, Switch } from "@netpro/design-system";
import { useFormContext } from "react-hook-form";

type Props = {
  name: string
  label?: string
  hideMessage?: boolean
  switchProps?: SwitchProps
  formItemProps?: ComponentPropsWithoutRef<typeof FormItem>
};

export const FormSwitch: FC<Props> = ({ name, label, hideMessage, switchProps, formItemProps }) => {
  const { control } = useFormContext()

  return (
    <FormField
      name={name}
      control={control}
      render={({
        field,
      }) => (
        <FormItem {...formItemProps}>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Switch
              checked={field.value}
              onCheckedChange={field.onChange}
              {...field}
              {...switchProps}
            />
          </FormControl>
          {!hideMessage && <FormMessage />}
        </FormItem>
      )}
    />
  )
}
