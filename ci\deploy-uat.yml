trigger: none

variables:
  APP_SERVICE_CONNECTION: 'PRD Application Service Connection'
  APP_SERVICE_NAME: 'app-pcp-mgt-prd-weu'
  RESOURCE_GROUP: 'rg-ttg-pcp-app-weu'

stages:
  - stage: Build
    # Only deploy tagged releases
    pool:
      vmImage: ubuntu-latest
    jobs:
      - job: Context
        condition: and(
          startsWith(variables['Build.SourceBranch'], 'refs/tags/'),
          not(contains(variables['Build.SourceBranch'], 'alpha')),
          not(contains(variables['Build.SourceBranch'], 'beta'))
          )
        steps:
          - script: |
              echo "Build.SourceBranch: $(Build.SourceBranch)"

      # Changelog should be generated with the Create Release pipeline
      # Creation of the changelog also creates a git tag which we're deploying in this pipeline
      - job: BuildApplication
        dependsOn: Context
        displayName: 'Checkout tag and Build application'
        steps:
          - template: templates/build-application.yml

  - stage: Deploy
    dependsOn: Build
    pool:
      name: TT PCP - LinuxAgents prd
    jobs:
    - deployment: DeployJob
      displayName: 'Deploy to UAT'
      environment: 'UAT'  # Adjust if you have specific environments set up in Azure DevOps
      strategy:
        runOnce:
          deploy:
            steps:

              - task: AzureWebApp@1
                displayName: 'Azure App Service Deploy: ${{ variables.APP_SERVICE_NAME }}'
                inputs:
                  azureSubscription: '${{ variables.APP_SERVICE_CONNECTION }}'
                  appType: 'webAppLinux'
                  appName: '${{ variables.APP_SERVICE_NAME }}'
                  deployToSlotOrASE: true
                  resourceGroupName: '${{ variables.RESOURCE_GROUP }}'
                  slotName: 'ACC'
                  package: '$(Pipeline.Workspace)/app/package.zip'
                  runtimeStack: 'NODE|20-lts'
                  startUpCommand: 'node ./node_modules/.bin/remix-serve ./build/server/index.js'
                  deploymentMethod: zipDeploy