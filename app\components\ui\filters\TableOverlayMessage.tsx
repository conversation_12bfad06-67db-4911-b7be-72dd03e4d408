import type { JSX, ReactNode } from "react";
import clsx from "clsx";

type Props = {
  isLoading?: boolean
  message?: string
  className?: string
  children?: ReactNode
};

export function TableOverlayMessage({
  className = "",
  children,
}: Props): JSX.Element {
  return (
    <div
      className={clsx(
        "transition-all duration-200 absolute backdrop-blur inset-0 bg-white/50 z-10 flex items-center justify-center",
        className,
      )}
    >
      <div className="flex flex-col items-center gap-1.5">
        {children}
      </div>
    </div>
  );
}
