import { z } from "zod";
import { optionalDateString } from "~/lib/utilities/zod-validators";

export const searchSchema = z.object({
  columns: z.string().array().optional(),
  submittedAfter: optionalDateString,
  submittedBefore: optionalDateString,
  financialPeriodStart: optionalDateString,
  financialPeriodEnd: optionalDateString,
  search: z.string().optional(),
  usingAccountingRecordsOnly: z.string().optional(),
  location: z.string().optional(),
  hideDeleted: z.enum(["true", "false", ""]).optional().nullable(),
});

export type SearchSchemaType = z.infer<typeof searchSchema>
