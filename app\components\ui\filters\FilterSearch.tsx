import type { FormEvent, JSX } from "react";
import { Button, Input, Spinner } from "@netpro/design-system";
import { Form, useNavigation, useSearchParams } from "@remix-run/react";
import clsx from "clsx";
import { Search, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

type SearchFilterProps = {
  placeholder?: string
  inputName?: string
  buttonText?: string
  className?: string
};

/**
 * @deprecated
 */
export function FilterSearch({
  placeholder = "Search...",
  inputName = "search",
  buttonText = "Search",
  className,
}: SearchFilterProps): JSX.Element {
  const [searchParams, setSearchParams] = useSearchParams();
  const [search, setSearch] = useState(searchParams.get(inputName) ?? "");
  const inputRef = useRef<HTMLInputElement>(null);
  const navigation = useNavigation();
  const setSearchValue = (value: string): void => {
    setSearch(value);
    setSearchParams((prev) => {
      if (!value) {
        prev.delete(inputName);

        return prev;
      }

      prev.set(inputName, value);

      return prev;
    });
  };
  const handleSubmit = (event: FormEvent): void => {
    event.preventDefault();
    const formData = new FormData(event.target as HTMLFormElement);
    const value = formData.get(inputName) as string;
    setSearchValue(value);
  };
  const handleClearSearch = (): void => {
    setSearchValue("");
    if (inputRef.current) {
      inputRef.current.value = "";
      inputRef.current.focus();
    }
  };

  // Effect to reset the search value when searchParams change
  useEffect(() => {
    const currentParam = searchParams.get(inputName) ?? "";
    setSearch(currentParam);
    if (inputRef.current) {
      inputRef.current.value = currentParam;
    }
  }, [searchParams, inputName]);

  return (
    <Form onSubmit={handleSubmit} className={clsx("w-full", className)}>
      <div className="flex items-center gap-2.5">
        <div className="relative w-full">
          <Input
            ref={inputRef}
            type="text"
            name={inputName}
            defaultValue={search}
            placeholder={placeholder}
            className="text-xs text-black h-10 flex-grow"
          />
          {search && (
            <a
              className="group absolute right-0 z-10 top-0 bottom-0 flex items-center justify-center px-3 cursor-pointer"
              onClick={handleClearSearch}
            >
              <span className="text-sm mr-1 group-hover:text-primary transition-all duration-150 w-0 group-hover:w-20 overflow-hidden text-nowrap">
                Clear search
              </span>
              <X className="group-hover:text-primary transition-all duration-150" size={16} />
            </a>
          )}
        </div>
        <Button type="submit" className="text-xs gap-1.5 text-white whitespace-nowrap" disabled={navigation.state === "loading"}>
          {navigation.state === "loading"
            ? (
                <Spinner className="size-4 mx-0 text-white" />
              )
            : (
                <Search size={16} />
              )}
          <span>{buttonText}</span>
        </Button>
      </div>
    </Form>
  );
}
