import type { AccessorKeyColumnDef, ColumnDef, DisplayColumnDef, GroupColumnDef } from "@tanstack/react-table";
import type { FC } from "react";
import type { FormMultiSelectProps } from "./FormMultiSelect";
import { FormMultiSelect } from "./FormMultiSelect"

type Props = {
  columns: (ColumnDef<any> | DisplayColumnDef<any> | AccessorKeyColumnDef<any, any> | GroupColumnDef<any, any>)[]
  label?: string
  triggerLabel?: string
}

// The things we do for typings
function getOptions(columns: Props["columns"]): FormMultiSelectProps["options"] {
  const options: FormMultiSelectProps["options"] = [];

  for (const column of columns) {
    if (column.id == null || column.header == null || typeof column.header !== "string") {
      continue
    }

    options.push({ label: column.header, key: column.id, checked: false })
  }

  // Alphabetically sort by label
  options.sort((a, b) => a.label.localeCompare(b.label))

  return options
}

/**
 * FormColumnsFilter is a functional component that provides a multi-select dropdown
 * for filtering table columns, built on top of `FormMultiSelect`. It transforms table column
 * definitions into selectable options and supports dynamic label customization.
 *
 * @template Props - The props type for the component.
 *
 * @param {object} props - The props for the component.
 * @param {(ColumnDef<any> | DisplayColumnDef<any>)[]} props.columns - An array of column definitions or display column definitions. Each column should have an `id` and a `header` for proper functionality.
 * @param {string} [props.label] - The label for the multi-select field.
 * @param {string} [props.triggerLabel] - The default label for the multi-select trigger button.
 *
 * @returns {JSX.Element} A form-integrated multi-select dropdown for column selection.
 *
 * @example
 * // Example usage with table column definitions
 * import { FormColumnsFilter } from './FormColumnsFilter';
 * import { createColumnHelper } from '@tanstack/react-table';
 *
 * const columnHelper = createColumnHelper<any>();
 * const columns = [
 *   columnHelper.accessor('name', { header: 'Name', id: 'name' }),
 *   columnHelper.accessor('age', { header: 'Age', id: 'age' }),
 *   columnHelper.display({ header: 'Actions', id: 'actions' }),
 * ];
 *
 * function MyTableFilters() {
 *   return (
 *     <FormColumnsFilter
 *       columns={columns}
 *       label="Visible Columns"
 *       triggerLabel="Select Columns"
 *     />
 *   );
 * }
 */
export const FormColumnsFilter: FC<Props> = ({ columns, label = "Visible Columns", triggerLabel = "All" }) => {
  return (
    <FormMultiSelect
      name="columns"
      label={label}
      triggerLabel={triggerLabel}
      closeOnSelect={false}
      options={getOptions(columns)}
    />
  )
}
