import { useLoaderData } from "@remix-run/react";
import type { RelevantActivityDeclarationSchemaType } from "~/features/economic-substance-tbah/types/relevant-activity-declaration-schema";
import { Pages } from "~/features/economic-substance-tbah/utilities/form-pages";
import { formatDate } from "~/lib/utilities/format";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";
import { SummaryTable } from "./table/SummaryTable";
import { SummaryTableData } from "./table/SummaryTableData";
import { SummaryTableRow } from "./table/SummaryTableRow";

export function RelevantActivities() {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { relevantActivities } = submissionData[Pages.RELEVANT_ACTIVITY_DECLARATION] as RelevantActivityDeclarationSchemaType
  const selectedRelevantActivities = relevantActivities?.filter(activity => activity.selected === "true")

  return (
    <div>
      <h2 className="text-blue-700 font-bold mb-4">Relevant Activities</h2>
      <SummaryTable>
        <thead className="font-bold">
          <SummaryTableRow>
            <SummaryTableData>From</SummaryTableData>
            <SummaryTableData>To</SummaryTableData>
            <SummaryTableData>Relevant Activity or Activities conducted</SummaryTableData>
          </SummaryTableRow>
        </thead>
        <tbody>
          {selectedRelevantActivities?.map(activity => (
            <SummaryTableRow key={activity.id}>
              <SummaryTableData>{formatDate(activity.startDate)}</SummaryTableData>
              <SummaryTableData>{formatDate(activity.endDate)}</SummaryTableData>
              <SummaryTableData>{activity.label}</SummaryTableData>
            </SummaryTableRow>
          ))}
        </tbody>
      </SummaryTable>
    </div>
  )
}
