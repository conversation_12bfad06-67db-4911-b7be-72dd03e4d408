import { z } from "zod";

export const editPendingOnboardingsSchema = z.object({
  strApproval: z.boolean(),
  boDirApproval: z.boolean(),
  bfrApproval: z.boolean(),
  esBahamasApproval: z.boolean(),
  strModuleEnabled: z.boolean(),
  boDirModuleEnabled: z.boolean(),
  bfrModuleEnabled: z.boolean(),
  esBahamasModuleEnabled: z.boolean(),
  submissionYear: z.string().optional(),
  bfrFee: z.coerce.number().int({
    message: "Submission fee must be an integer",
  }).max(9999, {
    message: "Submission fee must be less than 10000",
  }).nonnegative("Submission fee must be a positive number").optional(),
});

export type EditPendingOnboardingsSchemaType = z.infer<typeof editPendingOnboardingsSchema>;
