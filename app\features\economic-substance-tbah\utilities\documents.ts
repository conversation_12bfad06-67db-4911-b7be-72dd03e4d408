import type { DocumentDTO } from "~/services/api-generated";

export function mapDocumentIdsToKeys(dataSet: Record<string, string>, documentIds: string[]): Record<string, string> {
  return documentIds.reduce((map, docId) => {
    const matchingKey = Object.entries(dataSet).find(
      ([, value]) => value === docId,
    );
    if (matchingKey) {
      map[docId] = matchingKey[0];
    }

    return map;
  }, {} as Record<string, string>);
}

export function mapDocumentsToFormKeys(
  documentArray: DocumentDTO[],
  match: Record<string, string>,
) {
  return documentArray.reduce((acc, document) => {
    const formKey = match[document.id as string];
    if (formKey) {
      acc[formKey] = document;
    }

    return acc;
  }, {} as Record<string, DocumentDTO>);
}
