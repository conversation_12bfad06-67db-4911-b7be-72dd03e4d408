import type { ReactNode } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { SummaryTableCell } from "~/components/pages/SummaryTableCell";
import type { AddressOfHeadOfficeType } from "~/features/simplified-tax-return/schemas/address-of-head-office/2019/address-of-head-office-schema";
import { Pages } from "~/features/simplified-tax-return/utilities/form-pages";
import { useSubmission } from "~/features/submissions/context/use-submission";
import { getCountryName } from "~/lib/utilities/countries";
import { formatYesNoBoolean } from "~/lib/utilities/format";

export function AddressOfHeadOfficeSummary(): ReactNode {
  const { submissionData } = useSubmission();
  const addressOfHeadOffice = submissionData[Pages.ADDRESS_OF_HEAD_OFFICE] as AddressOfHeadOfficeType;

  return (
    <section id="addresss-section">
      <h2 className="text-lg font-semibold">Addresses of</h2>
      <Table className="border border-blue-600 pointer-events-none">
        <TableHeader>
          <TableRow>
            <TableHead className="w-1/2 text-lg font-semibold text-black border border-blue-600">Head Office</TableHead>
            <TableHead className="text-lg font-semibold text-black border border-blue-600">St. Kitts and Nevis</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow className="w-1/2 border-0">
            <SummaryTableCell
              label="Address #1"
              value={addressOfHeadOffice.address1}
              className="border-x border-blue-600"
            />
            <SummaryTableCell
              label="Address #1"
              value={addressOfHeadOffice.nevisAddress1}
              className="border-x border-blue-600"
            />
          </TableRow>
          <TableRow className="w-1/2 border-0">
            <SummaryTableCell
              label="Address #2"
              value={addressOfHeadOffice.address2}
              className="border-x border-blue-600"
            />
            <SummaryTableCell
              label="Address #2"
              value={addressOfHeadOffice.nevisAddress2}
              className="border-x border-blue-600"
            />
          </TableRow>

          <TableRow className="w-1/2 border-0">
            <TableCell className="py-1 border-x border-blue-600">
              <div className="grid grid-cols-2 ">
                <p>
                  City:
                  {" "}
                  <span className="font-semibold">{addressOfHeadOffice.city}</span>
                </p>
                <p>
                  Zip Code:
                  {" "}
                  <span className="font-semibold">{addressOfHeadOffice.zipCode}</span>
                </p>
              </div>
            </TableCell>

            <TableCell className="py-1 border-x border-blue-600">
              <div className="grid grid-cols-2 ">
                <p>
                  City:
                  {" "}
                  <span className="font-semibold">{addressOfHeadOffice.nevisCity}</span>
                </p>
                <p>
                  Zip Code:
                  {" "}
                  <span className="font-semibold">{addressOfHeadOffice.nevisZipCode}</span>
                </p>
              </div>
            </TableCell>
          </TableRow>

          <TableRow className="w-1/2 border-0">
            <SummaryTableCell
              label="Country"
              value={getCountryName(addressOfHeadOffice.country)}
              className="border-x border-blue-600"
            />
            <SummaryTableCell
              label="Country"
              value={getCountryName(addressOfHeadOffice.nevisCountry || "")}
              className="border-x border-blue-600"
            />
          </TableRow>

          <TableRow className="w-1/2 border-0">
            <SummaryTableCell
              label="Is the entity's Registered Address the offices of Trident Nevis?"
              value={formatYesNoBoolean(addressOfHeadOffice.isAddressInNevisDifferent)}
              className="border-x border-blue-600"
            />
            <TableCell></TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </section>
  );
}
