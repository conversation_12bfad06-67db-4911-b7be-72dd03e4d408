import { z } from "zod";
import { nonEmptyString, nonNullDate, stringBoolean } from "~/lib/utilities/zod-validators";

export const finalizeSchema = z.object({
  confirmationTrueInformation: stringBoolean(),
  confirmationUnderstand: stringBoolean(),
  confirmationAwarePerjury: stringBoolean(),
  dateOfSignature: nonNullDate("Date of signature"),
  addressOfPersonDeclaring: nonEmptyString("Address of person making the declaration"),
  addressOfPersonDeclaring2: z.string().optional(),
  zipCode: nonEmptyString("Zip code"),
  city: nonEmptyString("City"),
  country: nonEmptyString("Country"),
  nameOfPersonDeclaring: nonEmptyString("Name of person stating the declaration"),
  returnMade: z.array(z.any()).optional(),
  onMyOwnBehalf: z.string().optional(),
  asOfficer: z.string().optional(),
  asAttorney: z.string().optional(),
  asTrustee: z.string().optional(),
})
  .refine(data => !(data.confirmationTrueInformation !== "true"), {
    message: "Required.",
    path: ["confirmationTrueInformation"],
  })
  .refine(data => !(data.confirmationUnderstand !== "true"), {
    message: "Required.",
    path: ["confirmationUnderstand"],
  })
  .refine(data => !(data.confirmationAwarePerjury !== "true"), {
    message: "Required.",
    path: ["confirmationAwarePerjury"],
  })
  .refine(data => !(!data.onMyOwnBehalf && !data.asOfficer && !data.asAttorney && !data.asTrustee), {
    message: "Please specify at least one name.",
    path: ["returnMade", 0],
  });

export type FinalizeType = z.infer<typeof finalizeSchema>;
