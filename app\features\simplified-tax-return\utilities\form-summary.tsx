import type { JSX } from "react";
import type { FormYear } from "../types/form-year";
import { STRSummary as Summary2019 } from "~/features/simplified-tax-return/components/summaries/2019/STRSummary";
import { STRSummary as Summary2020 } from "~/features/simplified-tax-return/components/summaries/2020/STRSummary";
import { STRSummary as Summary2021 } from "~/features/simplified-tax-return/components/summaries/2021/STRSummary";
import { STRSummary as Summary2022To2024 } from "~/features/simplified-tax-return/components/summaries/2022-2024/STRSummary";

export const formSummary: Record<FormYear, (args: any) => JSX.Element> = {
  2019: ({ entityDetails }): JSX.Element => (
    <Summary2019 entityDetails={entityDetails} />
  ),
  2020: ({ entityDetails }): JSX.Element => (
    <Summary2020 entityDetails={entityDetails} />
  ),
  2021: ({ entityDetails }): JSX.Element => (
    <Summary2021 entityDetails={entityDetails} />
  ),
  2022: ({ entityDetails }): JSX.Element => (
    <Summary2022To2024 entityDetails={entityDetails} />
  ),
  2023: ({ entityDetails }): JSX.Element => (
    <Summary2022To2024 entityDetails={entityDetails} />
  ),
  2024: ({ entityDetails }): JSX.Element => (
    <Summary2022To2024 entityDetails={entityDetails} />
  ),
}
