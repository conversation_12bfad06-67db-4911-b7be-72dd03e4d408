import type { DetailedHTMLProps, HTMLAttributes, ReactNode } from "react";
import { cn } from "@netpro/design-system";

type Props = { children: ReactNode } & DetailedHTMLProps<HTMLAttributes<HTMLTableRowElement>, HTMLTableRowElement>

export function SummaryTableRow({ children, ...props }: Props) {
  return (
    <tr {...props} className={cn("border-blue-200 border-b-2 w-fit", props.className)}>
      {children}
    </tr>
  )
}
