import { z } from "zod";
import { nonNullDate } from "~/lib/utilities/zod-validators";

export const BusinessActivity = {
  BANKING_BUSINESS: "Banking Business",
  INSURANCE_BUSINESS: "Insurance Business",
  FUND_MANAGEMENT_BUSINESS: "Fund Management Business",
  FINANCE_AND_LEASING_BUSINESS: "Finance and Leasing Business",
  HEADQUARTERS_BUSINESS: "Headquarters Business",
  SHIPPING_BUSINESS: "Shipping Business",
  HOLDING_BUSINESS: "Holding Business",
  INTELLECTUAL_PROPERTY_BUSINESS: "Intellectual Property Business",
  DISTRIBUTION_SERVICE_CENTRE_BUSINESS: "Distribution and Service Centre Business",
  OTHER: "Other, please specify",
}

export const businessActivitySchema = z.object({
  activity: z.enum(["", ...Object.values(BusinessActivity)], {
    message: "Please select a business activity",
  }),
  otherActivity: z.string({
    required_error: "Please specify the other activity",
  }).optional(),
  from: nonNullDate("From date"),
  to: nonNullDate("To date"),
  type: z.enum(["Primary", "Secondary"], {
    message: "Please select a type of business activity",
  }),
}).refine(data => !(data.activity === BusinessActivity.OTHER && !data.otherActivity), {
  message: "Please specify the other activity",
  path: ["otherActivity"],
}).refine(data => ((data.from && data.to) && data.from < data.to), {
  message: "The 'To' date must be after the 'From' date",
  path: ["to"],
});

export const businessActivitiesFormSchema = z.object({
  activities: z.array(businessActivitySchema)
    .refine((businessActivities) => {
      const primaryBusinessActivities = businessActivities.filter(businessActivity => businessActivity.type === "Primary");

      return primaryBusinessActivities.length === 1;
    }, { message: "There must be exactly one primary business activity.", path: [0] })
    .refine((businessActivities) => {
      // Each activity can only be added once.
      const activities = businessActivities.map(businessActivity => businessActivity.activity);

      return activities.length === new Set(activities).size;
    }, { message: "Each activity can only be added once.", path: [1] }),
})

export type ActivityType = "Primary" | "Secondary"
export type BusinessActivityType = z.infer<typeof businessActivitySchema>;
export type BusinessActivitiesFormType = z.infer<typeof businessActivitiesFormSchema>;
