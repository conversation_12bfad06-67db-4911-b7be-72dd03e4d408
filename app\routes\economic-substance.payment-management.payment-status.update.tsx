import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { middleware } from "~/lib/middlewares.server";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type { MarkSubmissionsAsPaidByCompanyYearsRequestDTO } from "~/services/api-generated";
import { managementMarkAsPaidByCompanyAndYear } from "~/services/api-generated";

export const action = makeEnhancedAction(async ({ json, request }) => {
  await middleware(["auth"], request);
  const { module: strModule } = await requireActiveModule({ request, key: Modules.ECONOMIC_SUBSTANCE_BAHAMAS });
  const formData = await request.formData()
  const bodyData = formData.get("data") as string;
  const body = JSON.parse(bodyData) as MarkSubmissionsAsPaidByCompanyYearsRequestDTO
  const { data, error } = await managementMarkAsPaidByCompanyAndYear({
    headers: await authHeaders(request),
    body: {
      moduleId: strModule.id,
      ...body,
    },
  });

  if (error) {
    console.error(error);

    return json({
      success: false,
      message: "Failed to update payment status",
    }, { status: 400 });
  }

  return json({
    ...data,
    success: true,
  });
}, { authorize: ["es.bahamas.payments.import"] })
