import type { ReactNode } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { Form, useFormAction, useNavigate, useParams } from "@remix-run/react";
import * as AlertDialog from "~/components/AlertDialog";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { blockUnblockUser } from "~/services/api-generated";

export const action = makeEnhancedAction(async ({ params, request, setNotification, redirect }) => {
  const { id } = params
  await middleware(["auth"], request);
  const { error } = await blockUnblockUser({ headers: await authHeaders(request), path: { userId: id! }, body: {
    isBlocked: true,
  } })

  if (error) {
    setNotification({ title: "Failed to block user", variant: "error" })
  } else {
    setNotification({ title: "User has been blocked" })
  }

  return redirect(`/users/${id}`)
}, { authorize: ["users.block"] });

export const loader = makeEnhancedLoader(() => {
  return null;
}, { authorize: ["users.block"] })

export default function UserBlock(): ReactNode {
  const params = useParams();
  const navigate = useNavigate()
  const formAction = useFormAction();

  return (
    <AlertDialog.Root open onOpenChange={() => navigate(`/users/${params.id}`)}>
      <AlertDialog.Portal>
        <AlertDialog.Overlay />
        <AlertDialog.Content>
          <AlertDialog.Title>
            Are you sure you want to block this user?
          </AlertDialog.Title>
          <AlertDialog.Description>
            Blocking the user will prevent them from accessing and using the application.
          </AlertDialog.Description>
          <AlertDialog.Footer>
            <AlertDialog.Cancel asChild>
              <Button onClick={() => navigate("/users/")} variant="outline" size="sm">
                Cancel
              </Button>
            </AlertDialog.Cancel>
            <Form action={formAction} method="POST">
              <Button type="submit" variant="destructive" size="sm">
                Block
              </Button>
            </Form>
          </AlertDialog.Footer>
        </AlertDialog.Content>
      </AlertDialog.Portal>
    </AlertDialog.Root>
  )
}
