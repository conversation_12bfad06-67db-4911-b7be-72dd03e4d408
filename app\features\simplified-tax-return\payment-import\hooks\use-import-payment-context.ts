import type { ImportPaymentContextType } from "../context/ImportPaymentContext";
import { createContext, useContext } from "react";

export const ImportPaymentContext = createContext<ImportPaymentContextType | undefined>(undefined);

export function useImportPaymentContext(): ImportPaymentContextType {
  const context = useContext(ImportPaymentContext);
  if (!context) {
    throw new Error("useImportPaymentContext must be used within a ImportPaymentProvider");
  }

  return context;
}
