import { Checkbox } from "@netpro/design-system";
import { ReadableSubmissionStatusNames } from "~/features/submissions/utilities/submission-status";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeMakeColumn } from "~/lib/makeMakeColumn";
import type { ListSubmissionBahamasDTO, ManagementBahamasListSubmissionsByModuleData } from "~/services/api-generated";

type Type = "submissions" | "payments" | "ird-export"

type SortableColumns = NonNullable<NonNullable<ManagementBahamasListSubmissionsByModuleData["query"]>["SortBy"]>;

export const sortableColumnNames = Object.keys({
  LegalEntityName: null,
  LegalEntityCode: null,
  LegalEntityVPCode: null,
  MasterClientCode: null,
  Status: null,
  CreatedAt: null,
  PaymentMethod: null,
  PaymentReceivedAt: null,
  PaymentReference: null,
  FinancialPeriodEndsAt: null,
  CreatedByEmail: null, // Note: Not allowed yet by API
  InitialSubmittedAt: null, // Note: Not allowed yet by API
  IsPaid: null, // Note: Not allowed yet by API
  ReopenedAt: null, // Note: Not allowed yet by API
  ExportedAt: null, // Note: Not allowed yet by API
  IncorporationDate: null, // Note: Not allowed yet by API
  LegalEntityReferralOffice: null, // Note: Not allowed yet by API
  SubmittedAt: null, // Note: Not allowed yet by API (works but column is not sent in possible values)
} satisfies Record<SortableColumns, null>)
const makeColumn = makeMakeColumn<SortableColumns, ListSubmissionBahamasDTO>(sortableColumnNames)

export function useEsTbahColumns(type: Type) {
  const formatColDate = useFormatColDate();
  let columns = [];

  switch (type) {
    case "submissions":
      columns = [
        makeColumn({ header: "Email", id: "createdByEmail", accessorKey: "createdByEmail" }),
        makeColumn({ header: "Entity Name", id: "legalEntityName", accessorKey: "legalEntityName" }),
        makeColumn({ header: "Regulatory Code", id: "legalEntityCode", accessorKey: "legalEntityCode" }),
        makeColumn({ header: "Master Client Code", id: "masterClientCode", accessorKey: "masterClientCode" }),
        makeColumn({ header: "Status", id: "status", accessorKey: "status", cell: data => data.row.original.status ? ReadableSubmissionStatusNames[data.row.original.status] : data.row.original.status }),
        makeColumn({ header: "Date Created", id: "createdAt", accessorKey: "createdAt", cell: formatColDate("createdAt", { timezone: "Bahamas" }) }),
        makeColumn({ header: "Latest Submitted", id: "submittedAt", accessorKey: "submittedAt", cell: (data) => {
          const submittedAt = data.row.original.submittedAt;
          const initialSubmittedAt = data.row.original.initialSubmittedAt;

          if (submittedAt === initialSubmittedAt) {
            return "";
          }

          return submittedAt ? formatColDate("submittedAt", { timezone: "Bahamas" })(data as any) : "";
        } }),
        makeColumn({ header: "Initial Submitted", id: "initialSubmittedAt", accessorKey: "initialSubmittedAt", cell: formatColDate("initialSubmittedAt", { timezone: "Bahamas" }) }),
        makeColumn({ header: "Re-Opened Date", id: "reopenedAt", accessorKey: "reopenedAt", cell: (data) => {
          const reopenedAt = data.row.original.reopenedAt;

          // Only show if there are multiple revisions (reopenedAt will be null if only 1 revision)
          if (!reopenedAt) {
            return ""; // No reopening happened - only 1 revision
          }

          return formatColDate("reopenedAt", { timezone: "Bahamas" })(data as any);
        } }),
        makeColumn({ header: "Incorporation Date", id: "incorporationDate", accessorKey: "incorporationDate", cell: formatColDate("incorporationDate", { timezone: "UTC" }) }), // UTC since it's a date only
        makeColumn({ header: "Paid Date", id: "paymentReceivedAt", accessorKey: "paymentReceivedAt", cell: formatColDate("paymentReceivedAt", { timezone: "Bahamas" }) }),
        makeColumn({ header: "Payment Ref", id: "paymentReference", accessorKey: "paymentReference" }),
        makeColumn({ header: "Financial Period End Date", id: "financialPeriodEndsAt", accessorKey: "financialPeriodEndsAt", cell: formatColDate("financialPeriodEndsAt", { timezone: "UTC" }) }), // UTC since it's a date only
        makeColumn({ header: "Referral Office", id: "legalEntityReferralOffice", accessorKey: "legalEntityReferralOffice" }),
        makeColumn({ header: "None", id: "hasActivityNone", accessorKey: "hasActivityNone", cell: data => data.row.original.hasActivityNone ? "Yes" : "No" }),
        makeColumn({ header: "BB", id: "hasActivityBankingBusiness", accessorKey: "hasActivityBankingBusiness", cell: data => data.row.original.hasActivityBankingBusiness ? "Yes" : "No" }),
        makeColumn({ header: "IB", id: "hasActivityInsuranceBusiness", accessorKey: "hasActivityInsuranceBusiness", cell: data => data.row.original.hasActivityInsuranceBusiness ? "Yes" : "No" }),
        makeColumn({ header: "FMB", id: "hasActivityFundManagementBusiness", accessorKey: "hasActivityFundManagementBusiness", cell: data => data.row.original.hasActivityFundManagementBusiness ? "Yes" : "No" }),
        makeColumn({ header: "FLB", id: "hasActivityFinanceLeasingBusiness", accessorKey: "hasActivityFinanceLeasingBusiness", cell: data => data.row.original.hasActivityFinanceLeasingBusiness ? "Yes" : "No" }),
        makeColumn({ header: "HQ", id: "hasActivityHeadquartersBusiness", accessorKey: "hasActivityHeadquartersBusiness", cell: data => data.row.original.hasActivityHeadquartersBusiness ? "Yes" : "No" }),
        makeColumn({ header: "SB", id: "hasActivityShippingBusiness", accessorKey: "hasActivityShippingBusiness", cell: data => data.row.original.hasActivityShippingBusiness ? "Yes" : "No" }),
        makeColumn({ header: "IP", id: "hasActivityIntellectualPropertyBusiness", accessorKey: "hasActivityIntellectualPropertyBusiness", cell: data => data.row.original.hasActivityIntellectualPropertyBusiness ? "Yes" : "No" }),
        makeColumn({ header: "SC", id: "hasActivityHoldingBusiness", accessorKey: "hasActivityHoldingBusiness", cell: data => data.row.original.hasActivityHoldingBusiness ? "Yes" : "No" }),
      ];
      break;
    case "payments":
      columns = [
        makeColumn({
          id: "select",
          header: ({ table }) => (
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected()
                || (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => {
                if (value) {
                  table.toggleAllPageRowsSelected(true);
                } else {
                  table.resetRowSelection(false);
                }
              }}
              className="-translate-x-3 mx-1"
              aria-label="Select all"
            />
          ),
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={value => row.toggleSelected(Boolean(value))}
              aria-label="Select row"
              className="-translate-x-3 mx-1"
            />
          ),
          enableSorting: false,
          enableHiding: false,
        }),
        makeColumn({ header: "Entity Name", id: "legalEntityName", accessorKey: "legalEntityName" }),
        makeColumn({ header: "Regulatory Code", id: "legalEntityCode", accessorKey: "legalEntityCode" }),
        makeColumn({ header: "Master Client Code", id: "masterClientCode", accessorKey: "masterClientCode" }),
        makeColumn({ header: "Status", id: "isPaid", accessorKey: "status", cell: data => data.row.original.isPaid ? "Paid" : "Unpaid" }),
        makeColumn({ header: "Financial Period End Date", id: "financialPeriodEndsAt", accessorKey: "financialPeriodEndsAt", cell: formatColDate("financialPeriodEndsAt", { timezone: "UTC" }) }), // UTC since it's a date only
        makeColumn({ header: "Date Created", id: "createdAt", accessorKey: "createdAt", cell: formatColDate("createdAt", { timezone: "Bahamas" }) }),
        makeColumn({ header: "Submitted Date", id: "submittedAt", accessorKey: "submittedAt", cell: formatColDate("submittedAt", { timezone: "Bahamas" }) }),
      ];
      break;
    case "ird-export":
      columns = [
        makeColumn({
          id: "select",
          header: ({ table }) => (
            <Checkbox
              checked={
                table.getIsAllPageRowsSelected()
                || (table.getIsSomePageRowsSelected() && "indeterminate")
              }
              onCheckedChange={(value) => {
                if (value) {
                  table.toggleAllPageRowsSelected(true);
                } else {
                  table.resetRowSelection(false);
                }
              }}
              className="-translate-x-3 mx-1"
              aria-label="Select all"
            />
          ),
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={value => row.toggleSelected(Boolean(value))}
              aria-label="Select row"
              className="-translate-x-3 mx-1"
            />
          ),
          enableSorting: false,
          enableHiding: false,
        }),
        makeColumn({ header: "Entity Name", id: "LegalEntityName", accessorKey: "legalEntityName" }),
        makeColumn({ header: "Regulatory Code", id: "LegalEntityCode", accessorKey: "legalEntityCode" }),
        makeColumn({ header: "Status", id: "Status", accessorKey: "status" }),
        makeColumn({ header: "Submitted Date", id: "SubmittedAt", accessorKey: "submittedAt", cell: formatColDate("submittedAt", { timezone: "Bahamas" }) }),
        makeColumn({ header: "Exported At", id: "exportedAt", accessorKey: "exportedAt", cell: formatColDate("exportedAt", { timezone: "Bahamas" }) }),
        makeColumn({ header: "Resubmitted Date", id: "submittedAt", accessorKey: "initialSubmittedAt", cell: formatColDate("submittedAt", { timezone: "Bahamas" }) }),
        makeColumn({ header: "Financial Period Start Date", id: "financialPeriodStartsAt", accessorKey: "financialPeriodStartsAt", cell: formatColDate("financialPeriodStartsAt", { timezone: "UTC" }) }),
        makeColumn({ header: "Financial Period End Date", id: "financialPeriodEndsAt", accessorKey: "financialPeriodEndsAt", cell: formatColDate("financialPeriodEndsAt", { timezone: "UTC" }) }),
        makeColumn({ header: "Payment Method", id: "paymentMethod", accessorKey: "paymentMethod" }),
        makeColumn({ header: "Paid Date", id: "paymentReceivedAt", accessorKey: "paymentReceivedAt", cell: formatColDate("paymentReceivedAt", { timezone: "Bahamas" }) }),
        makeColumn({ header: "Payment Ref", id: "paymentReference", accessorKey: "paymentReference" }),
      ]
  }

  return { columns }
}
