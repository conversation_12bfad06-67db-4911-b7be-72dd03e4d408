import type { ReactNode } from "react";
import { type ActionFunctionArgs, redirect, type TypedResponse } from "@remix-run/node";
import { Outlet } from "@remix-run/react";
import { destroySession, getSession } from "~/lib/auth/utils/session.server";
import { destroyUserPermissionCookie } from "~/lib/cookiePermissions.server";

export async function action({ request }: ActionFunctionArgs): Promise<TypedResponse> {
  const headers = new Headers();
  const session = await getSession(request.headers.get("Cookie"));

  headers.append("Set-Cookie", await destroySession(session))
  headers.append("Set-Cookie", await destroyUserPermissionCookie())

  return redirect("/", { headers });
}

export default function Layout(): ReactNode {
  return <Outlet />;
}
