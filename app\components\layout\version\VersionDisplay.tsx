import type { ReactNode } from "react";
import { cn } from "@netpro/design-system";
import { useRouteLoaderData } from "@remix-run/react";
import type { loader as rootLoader } from "~/root";

type VersionDisplayProps = {
  className?: string
};

export function VersionDisplay({ className }: VersionDisplayProps): ReactNode {
  const data = useRouteLoaderData<typeof rootLoader>("root");
  // Use the appVersion and apiVersion from the loader data if available
  const appVersion = data?.appVersion;
  const apiVersion = data?.apiVersion;

  return (
    <div className={cn("flex items-start gap-2", className)}>
      <div className="flex flex-col gap-0 justify-center items-start">
        <span className="text-xs font-light text-gray-400">
          GUI version:
          {" "}
          {appVersion}
        </span>
        <span className="text-xs font-light text-gray-400">
          API version:
          {" "}
          {apiVersion}
        </span>
      </div>
    </div>
  );
}
