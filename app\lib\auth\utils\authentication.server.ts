import type {
  AuthenticationResult,
  AuthorizationUrlRequest,
  IdTokenClaims,
} from "@azure/msal-node";

import type {
  LoaderFunctionArgs,
  TypedResponse,
} from "@remix-run/node";
import {
  createClientApplication,
  createConfiguration,
  getAccessTokenByClientCredentials,
  getAccessToken as getMicrosoftEntraAccessToken,
  redirectToLogin as redirectToMicrosoftEntraLogin,
} from "@netpro/auth";
import {
  redirect,
} from "@remix-run/node";

import { sessionStorage } from "~/lib/auth/utils/session.server";
import { client } from "~/services/api-generated";

// Verify that the required environment variables are defined.
if (!process.env.ENTRA_REDIRECT_URI) {
  throw new Error("Missing ENTRA_REDIRECT_URI");
}

if (!process.env.ENTRA_CLIENT_ID) {
  throw new Error("Missing ENTRA_CLIENT_ID");
}

if (!process.env.ENTRA_TENANT_ID) {
  throw new Error("Missing ENTRA_TENANT_ID");
}

if (!process.env.ENTRA_CLIENT_SECRET) {
  throw new Error("Missing ENTRA_CLIENT_SECRET");
}

if (!process.env.ENTRA_API_SCOPE) {
  throw new Error("Missing ENTRA_API_SCOPE");
}

if (!process.env.API_BASE_URL) {
  throw new Error("Missing API_BASE_URL");
}

client.setConfig({ baseUrl: process.env.API_BASE_URL });

const applicationConfig = createConfiguration({
  clientId: process.env.ENTRA_CLIENT_ID,
  clientSecret: process.env.ENTRA_CLIENT_SECRET,
  tenantId: process.env.ENTRA_TENANT_ID,
  nodeEnv: process.env.NODE_ENV,
});
const clientApplication = createClientApplication(applicationConfig);
const authCodeUrlParameters: AuthorizationUrlRequest = {
  scopes: ["user.read"],
  redirectUri: process.env.ENTRA_REDIRECT_URI,
  prompt: "login",
};

export async function redirectToLogin(request: LoaderFunctionArgs["request"]): Promise<TypedResponse<never>> {
  return redirectToMicrosoftEntraLogin({
    request,
    authCodeUrlParameters,
    clientApplication,
    sessionStorage,
  });
}

export async function getClientCredentialsToken(): Promise<AuthenticationResult> {
  return getAccessTokenByClientCredentials({
    scopes: [process.env.ENTRA_API_SCOPE as string],
    clientApplication,
  });
}

export async function getAccessToken(
  request: LoaderFunctionArgs["request"],
  redirectTarget: string,
): Promise<TypedResponse<never>> {
  const response = await getMicrosoftEntraAccessToken({
    request,
    clientApplication,
    sessionStorage,
    entraRedirectUri: process.env.ENTRA_REDIRECT_URI as string,
  });
  const idTokenClaims = response.idTokenClaims as IdTokenClaims;
  const session = await sessionStorage.getSession(
    request.headers.get("Cookie"),
  );

  session.set("objectId", idTokenClaims.oid);
  session.set("userEmail", response.account?.username);
  session.unset("pkce");

  return redirect(redirectTarget ?? "/", {
    headers: {
      // Make sure to save session changes before redirecting
      "Set-Cookie": await sessionStorage.commitSession(session),
    },
  });
}
