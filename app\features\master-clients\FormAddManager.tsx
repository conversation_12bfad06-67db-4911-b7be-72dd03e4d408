import type { ReactNode } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button, Input, Spinner } from "@netpro/design-system";
import { Form, useFetcher, useParams } from "@remix-run/react";
import { useEffect } from "react";
import { useRemixForm } from "remix-hook-form";
import type { AddManagerSchemaType } from "~/features/master-clients/schemas/add-manager";
import { addManagerSchema } from "~/features/master-clients/schemas/add-manager";
import type { action } from "~/routes/_main._card.master-clients.$id.edit.manager.add";

const resolver = zodResolver(addManagerSchema);

type Props = {
  closeAddManager: () => void
}

export function FormAddManager({ closeAddManager }: Props): ReactNode {
  const params = useParams();
  const fetcher = useFetcher<typeof action>();
  const { handleSubmit, formState: { errors, isSubmitting }, register } = useRemixForm<AddManagerSchemaType>({
    mode: "onSubmit",
    submitConfig: {
      action: `/master-clients/${params.id}/edit/manager/add`,
    },
    resolver,
    fetcher,
  });

  useEffect(() => {
    if (fetcher.data && !("errors" in fetcher.data)) {
      closeAddManager()
    }
  }, [closeAddManager, fetcher.data]);

  return (
    <Form onSubmit={handleSubmit} method="POST" noValidate>
      <fieldset disabled={isSubmitting} className="flex flex-row items-start gap-0.5">
        <div className="flex flex-col w-full gap-0.5">
          <Input disabled={isSubmitting} type="email" {...register("managerEmail")} invalid={Boolean(errors?.managerEmail)} />
          {errors?.managerEmail && <p className="text-red-600">{errors.managerEmail.message}</p>}
        </div>
        <Button type="submit" size="sm">
          {isSubmitting ? <Spinner className="size-4 mx-0 text-white" /> : "Save"}
        </Button>
      </fieldset>
    </Form>
  );
}
