import { z } from "zod";

/**
 * Similar to `optionalEnumWithEmpty`, but supports an array of values.
 */
export function optionalEnumArrayWithEmpty<const T extends string[]>(values: T) {
  return z.preprocess(
    (value) => {
      if (Array.isArray(value)) {
        const filtered = value.filter(v => v !== "");

        return filtered.length > 0 ? filtered : undefined;
      }

      return undefined;
    },
    z.array(z.enum(values as unknown as [T[number], ...T])).optional(),
  );
}
