import { DEFAULT_FORMAT, formatDate, type Timezone } from "~/lib/utilities/format";

/*
 *  This hook wraps the formatDate function with a predefined format ("dd-MMM-yyyy").
 *  It provides backward compatibility for existing code while using the centralized
 *  formatDate function internally.
 *
 *  Consider migrating to direct formatDate calls for better flexibility and consistency.
 */

export type UseFormatDateOptions = {
  formatStr?: string
  timezone?: Timezone
  fallbackMessage?: string
}

export function useFormatDate(): (date: string | Date | null | undefined, options?: UseFormatDateOptions) => string {
  return (date: string | Date | null | undefined, options?: UseFormatDateOptions): string => {
    const { formatStr = DEFAULT_FORMAT, timezone = "UTC", fallbackMessage = "N/A" } = options || {}

    return formatDate(date, {
      timezone,
      formatStr,
      fallbackMessage,
    })
  }
}
