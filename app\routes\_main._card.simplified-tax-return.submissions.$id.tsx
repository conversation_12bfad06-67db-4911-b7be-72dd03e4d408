import { Button, cn } from "@netpro/design-system";
import { Link, Outlet, useLoaderData, useNavigate, useNavigation, useParams } from "@remix-run/react";
import { FileDown } from "lucide-react";
import { type ReactNode, useContext, useState } from "react";
import { useForm } from "react-hook-form";
import { ActionSheetActionRow } from "~/components/ActionSheetActionRow";
import { ActionSheetBody } from "~/components/ActionSheetBody";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetDescriptionList } from "~/components/ActionSheetDescriptionList";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { ActionSheetSection } from "~/components/ActionSheetSection";
import { Authorized } from "~/components/Authorized";
import { ContextUser } from "~/components/ContextUser";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { SubmissionStatusNamesEnum } from "~/features/submissions/utilities/submission-status";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { formatDate } from "~/lib/utilities/format";
import { managementGetSubmission } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, params, json, setNotification, redirect }) => {
  await middleware(["auth"], request);
  const submissionResponse = await managementGetSubmission({ headers: await authHeaders(request), path: { submissionId: params.id! } })

  if (!submissionResponse.data) {
    setNotification({ title: "Submission Not Found", message: "The requested submission could not be found", variant: "error" })

    return redirect(`/simplified-tax-return/submissions?${new URLSearchParams({ hideDeleted: "true" })}`);
  }

  return json({ submission: submissionResponse.data })
}, { authorize: ["str.submissions.view"] });

export default function SubmissionDetail(): ReactNode {
  const params = useParams();
  const navigate = useNavigate();
  const formMethods = useForm();
  const navigation = useNavigation();
  const { submission } = useLoaderData<typeof loader>();
  const { permissions } = useContext(ContextUser);
  const [isCancelling, setIsCancelling] = useState(false);

  function handleResetToSaved(): void {
    navigate(`/simplified-tax-return/submissions/${params.id}/reset-to-saved`)
  }

  return (
    <>
      <ActionSheetBody formMethods={formMethods}>
        <ActionSheetContent title="Submission Details">
          <ActionSheetSection title="Details" collapsible>
            <ActionSheetDescriptionList
              data={submission}
              headers={[
                ["legalEntityName", "Entity Name"],
                ["legalEntityCode", "Regulatory Code"],
                ["masterClientCode", "Master Client Code"],
                ["status", "Status"],
                ["isPaid", "Is Paid?", ({ isPaid }): string => isPaid ? "Yes" : "No"],
                ["createdAt", "Created Date", ({ createdAt }): string => createdAt ? formatDate(createdAt, { timezone: "Nevis", formatStr: "dd-MMM-yyyy" }) : ""],
                ["submittedAt", "Submitted Date", ({ submittedAt }): string => submittedAt ? formatDate(submittedAt, { timezone: "Nevis", formatStr: "dd-MMM-yyyy" }) : ""],
                ["paymentReceivedAt", "Paid Date", ({ paymentReceivedAt }): string => paymentReceivedAt ? formatDate(paymentReceivedAt, { timezone: "Nevis", formatStr: "dd-MMM-yyyy" }) : ""],
                ["financialYear", "Financial Year"],
                ["legalEntityReferralOffice", "Referral Office"],
              ]}
            />
          </ActionSheetSection>
          <Authorized oneOf={["str.submissions.reset", "str.rfi-request.start", "str.rfi-request.cancel", "str.rfi-request.view"]}>
            <ActionSheetSection title="Actions">
              <ActionSheetActionRow label="Reset Submission Status">
                <Button type="button" variant="destructive" size="sm" onClick={handleResetToSaved} disabled={submission.status !== "Submitted"}>
                  <span className="text-xs font-semibold">Reset to Saved</span>
                </Button>
              </ActionSheetActionRow>
              {submission.status !== SubmissionStatusNamesEnum.INFORMATION_REQUESTED && (
                <ActionSheetActionRow label="Request For Information">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => navigate(`/simplified-tax-return/submissions/${params.id}/rfi`)}
                    disabled={submission.status !== SubmissionStatusNamesEnum.SUBMITTED || !permissions?.includes("str.rfi-request.start")}
                  >
                    <span className="text-xs font-semibold">Start Request</span>
                  </Button>
                </ActionSheetActionRow>
              )}
              {submission.status === SubmissionStatusNamesEnum.INFORMATION_REQUESTED && (
                <ActionSheetActionRow label="Request for Information">
                  <Button
                    size="sm"
                    type="button"
                    variant="outline"
                    disabled={submission.status !== SubmissionStatusNamesEnum.INFORMATION_REQUESTED || !permissions?.includes("str.rfi-request.cancel")}
                    onClick={() => {
                      setIsCancelling(true);
                      navigate(`/simplified-tax-return/submissions/${params.id}/rfi/cancel`);
                    }}
                  >
                    <span className="text-xs">Cancel Request Information</span>
                  </Button>
                </ActionSheetActionRow>
              )}
            </ActionSheetSection>
          </Authorized>
          <ActionSheetSection title="Documents">
            <div className="flex-row space-y-2">
              {submission.status !== "Draft" && (
                <div>
                  <Link
                    to={`/simplified-tax-return/submissions/${params.id}/summary`}
                    className={cn("flex", !submission.submittedAt ? "pointer-events-none select-none" : "")}
                    target="_blank"
                  >
                    <Button className="flex items-center" type="button" size="sm" disabled={!submission.submittedAt}>
                      <FileDown size={14} className="mr-2" />
                      <span className="text-xs font-semibold">Download Submission</span>
                    </Button>
                  </Link>
                </div>
              )}
              <div>
                <Link
                  to={`/invoices/${submission.invoiceId}/file`}
                  className={cn("flex", !submission.invoiceId ? "pointer-events-none select-none" : "")}
                  target="_blank"
                >
                  <Button className="flex items-center" type="button" size="sm" disabled={!submission.invoiceId}>
                    <FileDown size={14} className="mr-2" />
                    <span className="text-xs font-semibold">Download Invoice</span>
                  </Button>
                </Link>
              </div>
            </div>
          </ActionSheetSection>
        </ActionSheetContent>
        <ActionSheetFooter />
      </ActionSheetBody>
      <LoadingState
        isLoading={isCancelling && navigation.state !== "idle"}
        message="Loading..."
      />
      <Outlet />
    </>
  )
}
