import { client } from "~/lib/api-client.server";

type Args = {
  id: string
  userId: string
}

export type GetUserResponse = {
  name: string
  surname: string
  username: string
  displayName: string
  email: string
  roleNames: any[]
  roleIds: any[]
  isActive: boolean
  isBlocked: boolean
  applicationUserRoles: any[]
  objectId: string
  id: string
}

export function getUser({ id, userId }: Args): Promise<GetUserResponse> {
  return client.get<GetUserResponse>(
    `/management/users/${id}`,
    userId,
  );
}
