import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@netpro/design-system";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import type { ListSubmissionDTO } from "~/services/api-generated";

export function usePaymentsColumns() {
  const formatColDate = useFormatColDate()
  const paymentsColumns: ColumnDef<ListSubmissionDTO>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected()
            || (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={value => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
    },
    { id: "LegalEntityName", header: "Entity Name", accessorKey: "legalEntityName", enableSorting: true },
    { id: "LegalEntityCode", header: "Regulatory Code", accessorKey: "legalEntityCode", enableSorting: true },
    { id: "LegalEntityVPCode", header: "VP Code", accessorKey: "legalEntityVPCode", enableSorting: true },
    { id: "MasterClientCode", header: "Master Client Code", accessorKey: "masterClientCode", enableSorting: true },
    { id: "Status", header: "Status", accessorKey: "isPaid", accessorFn: ({ isPaid }) => isPaid ? "Paid" : "Unpaid", enableSorting: true },
    { id: "PaymentReference", header: "TX ID", accessorKey: "txId", enableSorting: true },
    { id: "CreatedAt", header: "Date Created", accessorKey: "createdAt", cell: formatColDate("createdAt", { timezone: "Nevis" }), enableSorting: true },
    { id: "SubmittedAt", header: "Submitted Date", accessorKey: "submittedAt", cell: formatColDate("submittedAt", { timezone: "Nevis" }), enableSorting: true },
    { id: "FinancialYear", header: "Financial Year", accessorKey: "financialYear", enableSorting: true },
  ]

  return paymentsColumns
}
