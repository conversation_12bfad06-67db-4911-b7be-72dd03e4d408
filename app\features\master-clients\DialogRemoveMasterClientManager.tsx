import type { ReactNode } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>nt,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@netpro/design-system";
import { useFetcher, useParams } from "@remix-run/react";
import { X } from "lucide-react";
import { useEffect, useState } from "react";
import type { ListUserDTO } from "~/services/api-generated";

type Props = {
  user: ListUserDTO
}

type ActionReturnType = {
  errors: {
    managerEmail: string
  }
} | {
  success: boolean
}

export function DialogRemoveMasterClientManager({ user }: Props): ReactNode {
  const params = useParams();
  const fetcher = useFetcher<ActionReturnType>({ key: `MasterClientRemoveManager-${user.id}` });
  const [open, setOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    if (fetcher.data && "errors" in fetcher.data) {
      setErrorMessage(fetcher.data.errors.managerEmail);
    }
  }, [fetcher.data, user]);

  return (
    <Dialog open={open} onOpenChange={newOpenState => setOpen(newOpenState)}>
      <DialogTrigger asChild>
        <Button size="sm" variant="outline" name="intent" value="removeUser" className="h-6 px-2 group inline-flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
          <X size={12} className="text-destructive" />
          <span className="text-xs">Remove</span>
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Remove Trident manager</DialogTitle>
          <DialogDescription>
            {`Manager email address: ${user.email}`}
          </DialogDescription>
        </DialogHeader>
        {errorMessage && <Alert title={errorMessage} variant="error" />}
        <p className="text-foreground text-md">You are about to remove this manager from the Master Client.</p>
        <DialogFooter>
          <fetcher.Form
            method="post"
            action={`/master-clients/${params.id}/edit/manager/${user.id}/remove`}
            onSubmit={(event) => {
              event.preventDefault();
              setErrorMessage(null);
              if (fetcher.state !== "idle") {
                return;
              }

              fetcher.submit(event.currentTarget, { method: "POST" });
            }}
          >
            <fieldset disabled={fetcher.state === "submitting"} className="flex flex-row items-center gap-2">
              <Button type="button" size="sm" variant="secondary" onClick={() => setOpen(false)}>Cancel</Button>
              <input type="hidden" name="userId" value={user.id} />
              <Button size="sm" variant="destructive" className="place-self-end" type="submit">
                {fetcher.state !== "idle" ? "Removing..." : "Yes, remove this manager"}
              </Button>
            </fieldset>
          </fetcher.Form>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
