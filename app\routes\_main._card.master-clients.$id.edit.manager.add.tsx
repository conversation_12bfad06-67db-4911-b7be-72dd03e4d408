import type { FieldError, FieldErrors } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { redirect } from "@remix-run/react";
import { getValidatedFormData } from "remix-hook-form";
import type { AddManagerSchemaType } from "~/features/master-clients/schemas/add-manager";
import { addManagerSchema } from "~/features/master-clients/schemas/add-manager";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import type { MasterClientUserDTO } from "~/services/api-generated";
import { addUserToMasterClient } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async () => {
  return redirect("/master-clients");
}, { authorize: ["masterclients.trident-users.add"] })

export const action = makeEnhancedAction(async ({ json, request, params, session }) => {
  await middleware(["auth"], request);

  // Validate the form data and return any errors.
  const { errors, data } = await getValidatedFormData<AddManagerSchemaType>(request, zodResolver(addManagerSchema));
  if (errors) {
    return json({ errors });
  }

  // Save the newly added manager to the Master Client with an API request.
  const result = await addUserToMasterClient({
    headers: await authHeaders(request),
    path: {
      masterclientId: params.id as string,
    },
    body: {
      masterClientId: params.id as string,
      emailAddress: data.managerEmail,
    },
  });

  if ("error" in result && result.error) {
    session.flash("notification", {
      message: "An error occurred while adding the manager. Please try again.",
      title: "Manager not saved.",
      variant: "error",
    });

    return json({
      errors: {
        managerEmail: {
          message: "An error occurred while adding the manager. Please try again.",
        },
      } as FieldErrors<{ managerEmail: FieldError }>,
    });
  }

  const addedMasterClient = result.data as MasterClientUserDTO;
  session.flash("notification", { message: `${addedMasterClient.email} added to Master Client.`, title: "Manager saved." });

  return json({
    addedMasterClient,
  })
}, { authorize: ["masterclients.trident-users.add"] });
