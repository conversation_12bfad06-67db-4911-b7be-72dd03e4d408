import type { DisplayColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@netpro/design-system";
import { createColumnHelper } from "@tanstack/react-table";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import type { SubmissionDTO } from "~/services/api-generated";

const columnHelper = createColumnHelper<SubmissionDTO>();

export function usePaymentsColumns() {
  const formatColDate = useFormatColDate();
  const paymentsColumns: DisplayColumnDef<SubmissionDTO>[] = [
    columnHelper.display({
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected()
            || (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={value => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
    }),
    columnHelper.display({
      id: "companyName",
      header: "Entity Name",
      cell: props => props.row.original.legalEntityName,
    }),
    columnHelper.display({
      id: "companyNumber",
      header: "VP Code",
      cell: props => props.row.original.legalEntityCode,
    }),
    columnHelper.display({
      id: "masterClientCode",
      header: "Master Client Code",
      cell: props => props.row.original.masterClientCode,
    }),
    columnHelper.display({
      id: "status",
      header: "Status",
      cell: props => props.row.original.status,
    }),
    columnHelper.display({
      id: "createdAt",
      header: "Date Created",
      cell: formatColDate("createdAt", { timezone: "Panama" }),
    }),
    columnHelper.display({
      id: "submittedAt",
      header: "Submitted Date",
      cell: formatColDate("submittedAt", { timezone: "Panama" }),
    }),
  ]

  return paymentsColumns
}
