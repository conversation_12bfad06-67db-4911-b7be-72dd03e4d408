import type { PageSlug } from "~/features/economic-substance-tbah/utilities/form-pages";
import { SummaryPage } from "../SummaryPage";
import { Employees } from "./Employees";
import { IncomeExpenditure } from "./IncomeExpenditure";
import { Premises } from "./Premises";

export function BaseActivities({ page, label }: { page: PageSlug, label: string }) {
  return (
    <>
      <SummaryPage>
        <h2 className="text-blue-700 font-bold text-xl mb-4">{label}</h2>
        <IncomeExpenditure page={page} />
      </SummaryPage>
      <SummaryPage>
        <Employees page={page} />
      </SummaryPage>
      <SummaryPage>
        <Premises page={page} />
      </SummaryPage>
    </>
  )
}
