import { client } from "~/lib/api-client.server";

type Args = {
  userId: string
  pageNumber?: string | null
  pageSize?: number
  sortBy?: string | null
  sortOrder?: string | null
  filter?: string | null
}

export type GetUsersResponse = {
  pageNumber: number
  pageCount: number
  pageSize: number
  totalItemCount: number
  hasPrevious: boolean
  hasNext: boolean
  data: User[]
}

export type User = {
  name?: string
  surname?: string
  username?: string
  displayName?: string
  email?: string
  roleNames: any[]
  roleIds: any[]
  isActive: boolean
  isBlocked: boolean
  applicationUserRoles: ApplicationUserRole[]
  objectId?: string
  id: string
}

export type ApplicationUserRole = {
  userId: string
  roleId: string
}

export function getUsers({ userId, pageNumber, pageSize, sortBy, sortOrder, filter }: Args): Promise<GetUsersResponse> {
  const searchParams = new URLSearchParams();

  if (pageNumber != null) {
    searchParams.set("PagingInfo.PageNumber", pageNumber)
  }

  if (pageSize != null) {
    searchParams.set("PagingInfo.PageSize", String(pageSize))
  }

  if (sortBy != null) {
    searchParams.set("SortingInfo.SortBy", sortBy)
  }

  if (sortOrder != null) {
    searchParams.set("SortingInfo.SortOrder", sortOrder)
  }

  if (filter != null) {
    searchParams.set("Filter", filter)
  }

  return client.get<GetUsersResponse>(
    `/management/users?${searchParams.toString()}`,
    userId,
  );
}
