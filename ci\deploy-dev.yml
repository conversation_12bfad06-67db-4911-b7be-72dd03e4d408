trigger: none # Is triggered by the version bump pipeline

variables:
  APP_SERVICE_CONNECTION: 'DEV Infra Service Connection'
  APP_SERVICE_NAME: 'app-pcp-mgt-dev-eus2'
  RESOURCE_GROUP: 'rg-npdev-pcp-app-eus2'

stages:
  - stage: Build
    pool:
      vmImage: ubuntu-latest
    jobs:
      - job: BuildApplication
        displayName: 'Build application'
        steps:
          - template: templates/build-application.yml

  - stage: Deploy
    dependsOn: Build
    pool:
      name: TT PCP - LinuxAgents dev
    jobs:
    - deployment: DeployJob
      displayName: 'Deploy to Development'
      environment: 'Development'  # Adjust if you have specific environments set up in Azure DevOps
      strategy:
        runOnce:
          deploy:
            steps:
              - script: |
                  echo "===== Debugging context ======"
                  echo "> Pipeline.Workspace contents:"
                  cd $(Pipeline.Workspace)
                  ls -la
                  echo "-----"
                  echo "> ./app folder contents:"
                  cd app
                  ls -la
                  echo "===== End of context ======"

              - task: AzureWebApp@1
                displayName: 'Azure App Service Deploy: ${{ variables.APP_SERVICE_NAME }}'
                inputs:
                  azureSubscription: '${{ variables.APP_SERVICE_CONNECTION }}'
                  appType: 'webAppLinux'
                  appName: '${{ variables.APP_SERVICE_NAME }}'
                  deployToSlotOrASE: true
                  resourceGroupName: '${{ variables.RESOURCE_GROUP }}'
                  slotName: 'dev'
                  package: '$(Pipeline.Workspace)/app/package.zip'
                  runtimeStack: 'NODE|20-lts'
                  startUpCommand: 'node ./node_modules/.bin/remix-serve ./build/server/index.js'
                  deploymentMethod: zipDeploy
