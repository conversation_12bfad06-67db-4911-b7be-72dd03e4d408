import { z } from "zod";
import { preprocessArray, stringNumber } from "~/lib/utilities/zod-validators";
import { incomeSchema } from "./income-schema";

export const incomeDetailsSchema = z.object({
  totalInterestIncome: stringNumber({ invalidTypeMessage: "The total interest income received for the period is required.", greaterThan: 0 }),
  totalDividendIncome: stringNumber({ invalidTypeMessage: "The total dividend income received for the period is required.", greaterThan: 0 }),
  otherIncome: stringNumber({ invalidTypeMessage: "The other income received is required.", greaterThan: 0 }).optional(),
  otherIncomes: preprocessArray(z.array(incomeSchema)),
  endingInterestReceivable: stringNumber({ invalidTypeMessage: "Ending interest receivable is required.", greaterThan: 0 }),
  otherEndingInterestReceivable: preprocessArray(z.array(incomeSchema)),
  beginningInterestReceivable: stringNumber({ invalidTypeMessage: "Beginning interest receivable is required", greaterThan: 0 }),
  otherBeginningInterestReceivable: preprocessArray(z.array(incomeSchema)),
  totalProceeds: stringNumber({ invalidTypeMessage: "Total proceeds is required", greaterThan: 0 }).optional(),
  totalMarketValue: stringNumber({ invalidTypeMessage: "Total securities investments market value is required", greaterThan: 0 }),
  totalPurchaseCost: stringNumber({ invalidTypeMessage: "Total securities investment purchase cost required", greaterThan: 0 }),
})
  .refine(val => val.otherIncomes.length > 0, { message: "At least one item is required", path: ["otherIncomes", 0] })
  .refine(val => val.otherEndingInterestReceivable.length > 0, { message: "At least one item is required", path: ["otherEndingInterestReceivable", 0] })
  .refine(val => val.otherBeginningInterestReceivable.length > 0, { message: "At least one item is required", path: ["otherBeginningInterestReceivable", 0] })

export type IncomeDetailsSchemaType = z.infer<typeof incomeDetailsSchema>;
