import type { To } from "@remix-run/router";
import type { NavigateFunction, NavigateOptions } from "react-router";
import { useLocation, useNavigate } from "@remix-run/react";
import { useCallback } from "react";

export function usePreserveQueryNavigate(): (toOrDelta: To | number, options?: NavigateOptions) => void {
  const navigate: NavigateFunction = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const currentSearch = searchParams.toString();

  return useCallback(
    (toOrDelta: To | number, options?: NavigateOptions): void => {
      if (typeof toOrDelta === "number") {
        // Handle delta (e.g., history navigation)
        navigate(toOrDelta);
      } else {
        // Handle 'to' with options
        navigate(`${toOrDelta}?${currentSearch}`, options);
      }
    },
    [currentSearch, navigate],
  );
}
