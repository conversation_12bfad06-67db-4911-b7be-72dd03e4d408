import type { ReactNode } from "react";
import { <PERSON><PERSON>, Separator } from "@netpro/design-system";
import clsx from "clsx";
import { X } from "lucide-react";

export function FormFieldReset({ onReset, variant = "standalone" }: {
  onReset: () => void
  variant?: "select" | "standalone"
}): ReactNode {
  return (
    <div className={clsx(
      "absolute top-0 bottom-0 right-2 opacity-50 flex py-2",
      {
        "right-7": variant === "select",
      },
    )}
    >
      <Button variant="ghost" className="p-0 h-auto hover:bg-transparent" onClick={onReset} type="button">
        <X className="h-4" />
        <span className="sr-only">Reset field</span>
      </Button>
      {variant === "select" && (
        <Separator orientation="vertical" className="mr-1" />
      )}
    </div>
  )
}
