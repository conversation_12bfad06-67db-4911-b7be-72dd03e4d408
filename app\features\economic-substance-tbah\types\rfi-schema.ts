import { z } from "zod";
import { nonEmptyString, nonNullDate } from "~/lib/utilities/zod-validators";
import { createFileSchema } from "./file-schema";

const fieldName = "This field"
export const rfiSchema = z.object({
  deadLine: nonNullDate({ fieldName }),
  comments: nonEmptyString(fieldName),
  files: createFileSchema({ optional: true }),
})

export type RfiSchemaType = z.infer<typeof rfiSchema>
