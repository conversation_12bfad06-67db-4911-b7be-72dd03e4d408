import { submissionsSearchSchema } from "~/features/economic-substance-tbah/types/search-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type { SubmissionStatus } from "~/services/api-generated";
import { managementBahamasGenerateSubmissionsReport } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, setNotification, queryString }) => {
  await middleware(["auth"], request);
  const { module: bahamasESModule } = await requireActiveModule({ request, key: Modules.ECONOMIC_SUBSTANCE_BAHAMAS });
  const schemaData = submissionsSearchSchema.safeParse(queryString).data
  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });
  const { data, error, response } = await managementBahamasGenerateSubmissionsReport({ headers: await authHeaders(request), query: {
    ModuleId: bahamasESModule.id,
    PageNumber: pageNumber,
    SortOrder: orderDirection,
    SortBy: order as any,
    PageSize: pageSize,
    SubmittedAfterDate: schemaData?.submittedAfterDate,
    SubmittedBeforeDate: schemaData?.submittedBeforeDate,
    CompanyIncorporatedAfterDate: schemaData?.companyIncorporatedAfterDate,
    CompanyIncorporatedBeforeDate: schemaData?.companyIncorporatedBeforeDate,
    FinancialPeriodStartAt: schemaData?.financialPeriodStartAt,
    FinancialPeriodEndAt: schemaData?.financialPeriodEndAt,
    PaidAfterDate: schemaData?.paidAfterDate,
    PaidBeforeDate: schemaData?.paidBeforeDate,
    RelevantActivities: schemaData?.relevantActivities as string[] | undefined,
    Status: schemaData?.status as SubmissionStatus,
    ShowSubmitted: schemaData?.showSubmitted === "true" ? true : schemaData?.showSubmitted === "false" ? false : undefined,
    AllowReopen: schemaData?.allowReopen === "true" ? true : schemaData?.allowReopen === "false" ? false : undefined,
    GeneralSearchTerm: schemaData?.search,

  } })
  if (error) {
    setNotification({ title: "Error!", message: error.exceptionMessage as string || undefined, variant: "error" });

    return new Response(null, { status: response.status });
  }

  // Whitelist specific headers
  const whitelistedHeaders = new Headers();
  const allowedHeaders = ["Content-Type", "Content-Disposition"];
  for (const header of allowedHeaders) {
    const value = response.headers.get(header);
    if (value) {
      whitelistedHeaders.set(header, value);
    }
  }

  return new Response(data, {
    status: response.status,
    headers: whitelistedHeaders,
  });
}, { authorize: ["es.bahamas.submissions.export"] });
