export const SubmissionStatus = {
  DRAFT: 0,
  REVISION: 100,
  SUBMITTED: 200,
} as const;

export type SubmissionStatusType = typeof SubmissionStatus[keyof typeof SubmissionStatus];

export const SubmissionStatusNames = {
  DRAFT: "Draft",
  REVISION: "Revision",
  SCHEDULED: "Scheduled",
  SUBMITTED: "Submitted",
  PAID: "Paid",
  TEMPORAL: "Temporal",
  INFORMATION_REQUESTED: "InformationRequested",
} as const;

export enum SubmissionStatusNamesEnum {
  DRAFT = "Draft",
  REVISION = "Revision",
  SCHEDULED = "Scheduled",
  SUBMITTED = "Submitted",
  PAID = "Paid",
  TEMPORAL = "Temporal",
  INFORMATION_REQUESTED = "InformationRequested",
}

export const ReadableSubmissionStatusNames = {
  Draft: "Draft",
  Revision: "Revision",
  Scheduled: "Scheduled",
  Submitted: "Submitted",
  Paid: "Paid",
  Temporal: "Temporal",
  InformationRequested: "Information Requested",
}
