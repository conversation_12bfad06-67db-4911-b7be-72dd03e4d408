import type { FileUploaderProps } from "@netpro/design-system";
import type { ReactNode } from "react";
import type { FileRejection } from "react-dropzone";
import { Dropzone, FileUploader as NetProFileUploader, notify } from "@netpro/design-system";
import { CloudUpload } from "lucide-react";
import { sanitizeFilename } from "~/lib/utilities/files";

function handleRejections(rejections: FileRejection[]) {
  rejections.forEach((rejection) => {
    notify({
      title: "File Rejected",
      message: `${rejection.file.name} was rejected. Reason: ${rejection.errors[0].code}`,
      variant: "error",
      duration: 5000,
    });
  });
}

type Props = {
  children: ReactNode
} & FileUploaderProps

export function FileUploader({ children, setFiles, files, ...props }: Props) {
  const handleFileChange = (newFiles: File[]) => {
    const sanitizedFiles: File[] = [];
    const renamedFiles: string[] = [];

    newFiles.forEach((file) => {
      const sanitizedName = sanitizeFilename(file.name);

      if (sanitizedName !== file.name) {
        renamedFiles.push(`"${file.name}" → "${sanitizedName}"`);
      }

      const sanitizedFile = new File([file], sanitizedName, { type: file.type });
      sanitizedFiles.push(sanitizedFile);
    });

    // Show notification if files were renamed
    if (renamedFiles.length > 0) {
      notify({
        title: "Files Renamed for Security",
        message: renamedFiles.join(", "),
        variant: "warning",
        duration: 6000,
      });
    }

    // Replace existing files
    setFiles?.(sanitizedFiles);
  };

  return (
    <NetProFileUploader
      {...props}
      files={files}
      setFiles={handleFileChange}
      onReject={handleRejections}
    >
      <Dropzone className="flex h-36 flex-col gap-2 border-gray-300 p-3">
        <CloudUpload className="size-10 text-primary" />
        {children}
      </Dropzone>
    </NetProFileUploader>
  )
}
