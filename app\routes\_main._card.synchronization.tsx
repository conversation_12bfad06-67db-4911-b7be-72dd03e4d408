import type { ReactNode } from "react";
import { Alert } from "@netpro/design-system";
import { useLoaderData } from "@remix-run/react";
import { CardContainer } from "~/components/CardContainer";
import { SynchronizationStatusCard } from "~/features/synchronization/components/SynchronizationStatusCard";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementGetViewPointSyncStatus } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Synchronization",
    to: "/synchronization",
  },
  title: "Synchronization",
}

export const loader = makeEnhancedLoader(async ({ request, json }) => {
  await middleware(["auth"], request);
  const { data, error } = await managementGetViewPointSyncStatus({ headers: await authHeaders(request) });

  if (error) {
    throw new Response("Currently unable to retrieve View Point Synchronization Status", { status: 412 });
  }

  return json({
    syncStatus: data,
  });
}, { authorize: ["str.data-migration"] })

export default function SynchronizationLayout(): ReactNode {
  const { syncStatus } = useLoaderData<typeof loader>();

  return (
    <CardContainer>
      {syncStatus?.syncDetails && Object.keys(syncStatus?.syncDetails).length > 0
        ? (
            <div className="grid grid-cols-2 gap-4">
              <SynchronizationStatusCard entityName="Companies" syncDetails={syncStatus.syncDetails.companies} />
              <SynchronizationStatusCard entityName="Master Clients" syncDetails={syncStatus.syncDetails.masterclients} />
              <SynchronizationStatusCard entityName="Beneficial Owners" syncDetails={syncStatus.syncDetails.beneficialowners} />
              <SynchronizationStatusCard entityName="Officers" syncDetails={syncStatus.syncDetails.directors} />
            </div>
          )
        : (
            <Alert variant="warning" title="No synchronization details available" />
          )}
    </CardContainer>
  )
}
