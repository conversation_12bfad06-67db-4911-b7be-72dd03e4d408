import type { UpdateCompanyModulesRequest, UpdateCompanyModulesResponse } from "../types/company";
import { client } from "~/lib/api-client.server";

export async function sendCompanyModuleUpdateRequest(
  companyId: string,
  userId: string,
  request: UpdateCompanyModulesRequest,
): Promise<UpdateCompanyModulesResponse> {
  return client.put<UpdateCompanyModulesResponse>(
    `/management/companies/${companyId}/modules`,
    userId,
    request,
  );
}
