import type { JSX } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SmartPagination } from "@netpro/design-system";

type Props = {
  startIndex: number
  endIndex: number
  totalItems: number
  itemsPerPage: number
  currentPage: number
  onItemsPerPageChange: (value: number) => void
  setCurrentPage: (page: number) => void
}

export function MasterClientFooter({
  startIndex,
  endIndex,
  totalItems,
  itemsPerPage,
  currentPage,
  onItemsPerPageChange,
  setCurrentPage,
}: Props): JSX.Element {
  return (
    <div className="mt-4 flex justify-between items-center">
      <div className="text-xs font-bold whitespace-nowrap">
        {`Showing ${startIndex} to ${endIndex} of ${totalItems} entries`}
      </div>
      <div className="flex items-center justify-between w-full">
        <SmartPagination
          totalItems={totalItems}
          pageSize={itemsPerPage}
          currentPage={currentPage}
        />
        <div className="flex items-center">
          <span className="mr-2 font-bold text-xs whitespace-nowrap">
            Rows per page:
          </span>
          <Select
            value={itemsPerPage.toString()}
            onValueChange={(value) => {
              onItemsPerPageChange(Number(value));
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="w-[70px] font-bold">
              {itemsPerPage}
            </SelectTrigger>
            <SelectContent>
              {[5, 10, 15, 20, 25, 50, 100].map(value => (
                <SelectItem key={value} value={value.toString()}>
                  {value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
