import { redirect } from "@remix-run/react";
import { getSessionData } from "~/lib/auth/utils/session.server";
import type { MiddlewareProps, MiddlewareResponse } from "~/lib/middlewares.server";

type AuthenticatedSessionData = {
  userId: string
  validSession: true
};

export default async function auth({ request }: MiddlewareProps): MiddlewareResponse<never | AuthenticatedSessionData> {
  const { userId } = await getSessionData(request);
  if (!userId) {
    throw redirect("/login");
  }
  // Removed token expiration check. API access token should not be handled in the browser session.

  return { userId, validSession: true }
}
