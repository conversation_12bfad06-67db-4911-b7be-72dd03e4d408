import type { LoaderFunctionArgs, TypedResponse } from "@remix-run/node";
import { redirect } from "@remix-run/react";
import { redirectToLogin } from "~/lib/auth/utils/authentication.server";
import { middleware } from "~/lib/middlewares.server";

/**
 * The index route does the same redirection since the landing page
 * was removed as per ticket #13809.
 * To ensure backwards compatibility and to leave a direct link to
 * login functionality in the application, we're keeping this route
 * as well.
 */
export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse> {
  try {
    await middleware(["auth"], request);

    // User is already logged in, redirect to dashboard
    return redirect("/dashboard");
  } catch (ResponseError) {
    // User is not logged in, redirect to login
    return redirectToLogin(request);
  }
}
