import type { ComponentProps } from "react";
import { Input } from "@netpro/design-system";
import { X } from "lucide-react";
import { makeFormField } from "~/lib/makeFormField";

type Props = {
  inputProps?: ComponentProps<typeof Input>
}

export const FormSearch = makeFormField<Props>({ displayName: "FormSearch", render: ({ field, fieldState, inputProps }) => {
  return (
    <div className="relative w-full">
      <Input invalid={!!fieldState.error} {...field} {...inputProps} />
      {field.value && (
        <a
          className="group absolute right-0 z-10 top-0 bottom-0 flex items-center justify-center px-3 cursor-pointer"
          onClick={() => field.onChange("")}
        >
          <span className="text-sm mr-1 group-hover:text-primary transition-all duration-150 w-0 group-hover:w-20 overflow-hidden text-nowrap">
            Clear search
          </span>
          <X className="group-hover:text-primary transition-all duration-150" size={16} />
        </a>
      )}
    </div>
  )
} })
