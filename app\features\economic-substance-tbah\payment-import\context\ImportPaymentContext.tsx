import type { Dispatch, ReactNode, SetStateAction } from "react";
import { useMemo, useState } from "react";
import type { PaginatedResult } from "~/features/master-clients/types";
import type { SubmissionPaidStatusDto } from "~/services/api-generated";
import { ImportPaymentContext } from "../hooks/use-import-payment-context";

export type ImportPaymentContextType = {
  files: File[]
  setFiles: Dispatch<SetStateAction<File[]>>
  tableData: PaginatedResult<SubmissionPaidStatusDto> | undefined
  setTableData: Dispatch<SetStateAction<PaginatedResult<SubmissionPaidStatusDto> | undefined>>
  sheetRows: string[][] | undefined
  setSheetRows: Dispatch<SetStateAction<string[][] | undefined>>
}

export function ImportPaymentProvider({ children }: { children: ReactNode }) {
  const [files, setFiles] = useState<File[] >([]);
  const [sheetRows, setSheetRows] = useState<string[][] | undefined>()
  const [tableData, setTableData] = useState<PaginatedResult<SubmissionPaidStatusDto> | undefined>(undefined);
  const value = useMemo(() => ({
    files,
    setFiles,
    tableData,
    setTableData,
    sheetRows,
    setSheetRows,
  }), [files, tableData, sheetRows])

  return (
    <ImportPaymentContext.Provider value={value}>
      {children}
    </ImportPaymentContext.Provider>
  );
}

;
