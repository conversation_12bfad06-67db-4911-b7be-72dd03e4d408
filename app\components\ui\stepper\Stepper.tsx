import type { ReactNode } from "react";
import type { Step } from "~/lib/types/step";

type Props = { steps: Step[] };

export function Stepper({ steps }: Props): ReactNode {
  return (
    <nav aria-label="Progress">
      <ol className="space-y-4 md:flex md:space-x-8 md:space-y-0">
        {steps.map(step => (
          <li key={step.name} className="md:flex-1">
            {step.status === "complete"
              ? (
                  <div className="group flex cursor-default flex-col border-l-4 border-blue-600 py-2 pl-4 hover:border-blue-800 md:border-l-0 md:border-t-4 md:pb-0 md:pl-0 md:pt-4">
                    <span className="text-sm font-medium text-blue-600 group-hover:text-blue-800">{step.id}</span>
                    <span className="text-sm font-medium">{step.name}</span>
                  </div>
                )
              : step.status === "current"
                ? (
                    <div
                      className="flex cursor-default flex-col border-l-4 border-blue-600 py-2 pl-4 md:border-l-0 md:border-t-4 md:pb-0 md:pl-0 md:pt-4"
                      aria-current="step"
                    >
                      <span className="text-sm font-medium text-blue-600">{step.id}</span>
                      <span className="text-sm font-medium">{step.name}</span>
                    </div>
                  )
                : (
                    <div className="group flex cursor-default flex-col border-l-4 border-gray-200 py-2 pl-4 hover:border-gray-300 md:border-l-0 md:border-t-4 md:pb-0 md:pl-0 md:pt-4">
                      <span className="text-sm font-medium text-gray-500 group-hover:text-gray-700">{step.id}</span>
                      <span className="text-sm font-medium">{step.name}</span>
                    </div>
                  )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
