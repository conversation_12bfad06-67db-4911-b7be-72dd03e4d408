import type { ReactNode } from "react";
import { StepperContextProvider } from "~/components/StepperContext";
import { Stepper } from "~/components/ui/stepper/Stepper";
import { useStepper } from "~/lib/hooks/useStepper";
import { initialSteps } from "../utilities/initial-steps";
import { PaymentImportStep1 } from "./PaymentImportStep1";
import PaymentImportStep2 from "./PaymentImportStep2";
import { PaymentImportStep3 } from "./PaymentImportStep3";

export function PaymentImportContainer(): ReactNode {
  const { steps, currentStep, setCurrentStep } = useStepper(initialSteps, 1)

  return (
    <StepperContextProvider currentStep={currentStep} setCurrentStep={setCurrentStep}>
      <div className="w-full">
        <Stepper steps={steps} />
        {currentStep === 1 && <PaymentImportStep1 />}
        {currentStep === 2 && <PaymentImportStep2 />}
        {currentStep === 3 && <PaymentImportStep3 />}
      </div>
    </StepperContextProvider>
  )
}
