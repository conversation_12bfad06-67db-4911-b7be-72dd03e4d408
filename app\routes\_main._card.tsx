import type { ReactNode } from "react";
import { Breadcrumb, Bread<PERSON>rum<PERSON><PERSON><PERSON>, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@netpro/design-system";
import { Outlet, useMatches } from "@remix-run/react";
import { Fragment } from "react";
import type { BreadcrumbHandle } from "~/lib/types/breadcrumb-handle";

export const handle = {
  breadcrumb: {
    label: "Dashboard",
    to: "/dashboard",
  },
}

type MatchWithBreadcrumb = {
  handle?: {
    breadcrumb: BreadcrumbHandle
    title: string
    suffix?: string
  }
  pathname: string
  id: string
};

export default function CardLayout(): ReactNode {
  const matches = useMatches() as MatchWithBreadcrumb[];
  const lastMatch = matches
    .slice()
    .reverse()
    .find(match => match.handle?.title) as MatchWithBreadcrumb

  return (
    <main className="flex flex-col min-h-screen py-9 px-10 lg:pl-[290px] bg-gray-50">
      <header className="flex flex-col gap-0.5 w-full pb-7">
        <Breadcrumb>
          <BreadcrumbList>
            {matches
              .filter((match) => {
                return match?.handle?.breadcrumb as BreadcrumbHandle
                  && match?.handle?.breadcrumb.to !== lastMatch.pathname
              })
              .map((match, index, collection) => (
                <Fragment key={match.id}>
                  <BreadcrumbItem>
                    <BreadcrumbLink to={match?.handle?.breadcrumb.to as string}>
                      {match?.handle?.breadcrumb.label as string}
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  {collection.length > 1 && index < collection.length - 1 && <BreadcrumbSeparator />}
                </Fragment>
              ))}
          </BreadcrumbList>
        </Breadcrumb>

        <h2 className="text-3xl font-semibold font-inter capitalize">
          {lastMatch.handle && lastMatch.handle.title}
          {lastMatch.handle && lastMatch.handle.suffix && <span className="text-blue-700 normal-case">suff</span>}
        </h2>
      </header>
      <section className="flex grow w-full rounded border border-gray-200 py-4 px-5 gap-2.5 bg-white">
        <Outlet />
      </section>
    </main>
  );
}
