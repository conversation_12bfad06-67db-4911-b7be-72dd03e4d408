import { isAfter } from "date-fns";
import { z } from "zod";
import { nonNullDate } from "~/lib/utilities/zod-validators";

export const editLateFeeSchema = z.object({
  financialYear: z.coerce.string({ required_error: "Please provide a year." }).min(4, { message: "Please provide a year." }).max(4, { message: "Please provide a valid year." }).pipe(z.coerce.number()),
  amount: z.coerce.number().int({
    message: "Fee must be an integer",
  }).max(9999, {
    message: "Fee must be less than 10000",
  }).gt(0, "Fee must be greater than 0").optional(),
  description: z.string().optional(),
  invoiceText: z.string().optional(),
  startAt: nonNullDate({ message: "Please provide a start date" }),
  endAt: nonNullDate({ message: "Please provide an end date" }),
}).refine((data) => {
  if (!data.startAt || !data.endAt) {
    return true; // Skip if dates are not set
  }

  return isAfter(data.endAt, data.startAt);
}, {
  message: "Start date cannot be later than the end date",
  path: ["startAt"],
});

export type EditLateFeeSchemaType = z.infer<typeof editLateFeeSchema>;
