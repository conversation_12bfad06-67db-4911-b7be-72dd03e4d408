import type { FinancialPeriodSchemaType } from "../../types/financial-period-schema";
import { useLoaderData } from "@remix-run/react";
import { useFormatDate } from "~/lib/hooks/useFormatDate";
import type { BasicFinancialReportSummaryLoader } from "~/routes/_pdf.basic-financial-report.$id.summary";
import { EntityRelation, type FinalizeSchemaType } from "../../types/finalize-schema";
import { Pages } from "../../utilities/form-pages";

export function FrontPage() {
  const formatDate = useFormatDate()
  const { submissionData, entityDetails } = useLoaderData<BasicFinancialReportSummaryLoader>()
  const { legalEntityName, companyIdentityCode, masterClientCode, status, submittedAt } = entityDetails
  const submissionDate = submittedAt ? formatDate(submittedAt) : "N/A"
  const finalizePage = submissionData[Pages.FINALIZE] as FinalizeSchemaType
  let declarantName = "[Your name]"
  let address = "[Your address]"
  let entityRelation = "[Your relation to entity]"
  let phoneNumber = "[Your phone number]"
  let email = "[Your email]"
  if (finalizePage) {
    declarantName = finalizePage.declarantName
    address = finalizePage.address
    entityRelation = finalizePage.entityRelation !== EntityRelation.OTHER ? finalizePage.entityRelation : finalizePage.otherEntityRelation as string
    phoneNumber = `${finalizePage.telephone.prefix} ${finalizePage.telephone.number}`
    email = finalizePage.email
  }

  const { startFiscalYear, endFiscalYear } = submissionData[Pages.FINANCIAL_PERIOD] as FinancialPeriodSchemaType
  const startDate = startFiscalYear ? formatDate(startFiscalYear) : "N/A"
  const endDate = endFiscalYear ? formatDate(endFiscalYear) : "N/A"

  return (
    <div className="max-w-4xl mx-auto p-8 text-sm">
      <h1 className="text-blue-700 text-xl font-bold mb-6">
        Panama Financial Return
      </h1>

      {/* Entity Details */}
      <div className="mb-8">
        <h2 className="text-blue-700 font-bold mb-4">Entity Details</h2>
        <div className="border-2 border-blue-200 p-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <span>Entity Name: </span>
                <span className="font-semibold">
                  {legalEntityName}
                </span>
              </div>
              <div>
                <span>Company Identity Code: </span>
                <span className="font-semibold">
                  {companyIdentityCode}
                </span>
              </div>
              <div>
                <span>Master Client Code: </span>
                <span className="font-semibold">
                  {masterClientCode}
                </span>
              </div>
              <div>
                <span>Registered Agent: </span>
                <div className="mt-1">
                  <span className="font-semibold">
                    Trident Trust Company (TPAN) Limited
                  </span>
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <span className="font-semibold">Fiscal Period:</span>
                <div className="mt-3">
                  <div className="flex gap-1">
                    <div className="text-sm">Start Date:</div>
                    <div>{startDate}</div>
                  </div>
                  <div className="flex gap-1">
                    <div className="text-sm">End Date:</div>
                    <div>{endDate}</div>
                  </div>
                </div>
              </div>
              <div className="mt-4">
                <span>Submitted Date: </span>
                <span className="font-semibold">
                  {submissionDate}
                </span>
              </div>
              <div className="mt-4">
                <span>Status: </span>
                <span className="font-bold">{status}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Declaration */}
      <div className="mb-8">
        <h2 className="text-blue-700 font-bold mb-4">Declaration</h2>
        <div className="border-2 border-blue-200 p-4">
          <div className="mb-4">I confirm that:</div>
          <ul className="list-disc pl-5 space-y-1">
            <li>
              The information provided and represented in this portal and any document/s uploaded
              in conjunction with completing this accounting portal is/are, to the best of my knowledge and belief,
              true and correct.
            </li>
            <li>
              Aside from the represented and listed assets and liabilities in the form;
              no other assets and liabilities owned and incurred by the Company are in existence based
              on our knowledge.
            </li>
            <li>
              The assets contributed to the Company and the funds used to pay for all Trident's
              services are from lawful sources.
            </li>
            <li>
              The Financial Reports prepared from this accounting portal are for client purposes only
              and no audit was conducted.
            </li>
            <li>
              Any accounting treatment applied (and, in particular, the segregation of income) may not satisfy
              any regulatory or tax filing requirements. If any information contained herein is to be relied on
              for such filing, independent tax or legal advice should be obtained.
            </li>
            <li>
              I confirm and acknowledge that a submission fee in the amount of US$ 107.00 is due and payable
              in order to complete the submission process.
            </li>
          </ul>
        </div>
      </div>

      {/* Contact Details */}
      <div>
        <div className="border-2 border-blue-200 p-4">
          <div className="space-y-2">
            <div>
              <span className="font-semibold">Name of the person stating the declaration: </span>
              {declarantName}
            </div>
            <div>
              <span>Address: </span>
              {address}
            </div>
            <div>
              <span>Relation to entity: </span>
              {entityRelation}
            </div>
            <div>
              <span>Phone number: </span>
              {phoneNumber}
            </div>
            <div>
              <span>Email: </span>
              {email}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
