import { redirect } from "@remix-run/react";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { middleware } from "~/lib/middlewares.server";
import { postMigrationCleanup } from "~/services/api-generated";

export const action = makeEnhancedAction(async ({ request }) => {
  await middleware(["auth"], request);

  await postMigrationCleanup({ headers: await authHeaders(request) })

  return redirect("/data-migration")
}, { authorize: ["str.data-migration"] })
