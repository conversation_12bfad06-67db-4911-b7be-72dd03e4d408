import { z } from "zod";
import { phoneSchema } from "~/lib/schemas/phone-schema";
import { nonEmptyString, stringBoolean } from "~/lib/utilities/zod-validators";

export enum EntityRelation {
  DIRECTOR = "Director",
  SOLE_DIRECTOR = "Sole Director",
  ALTERNATE_DIRECTOR = "Alternate Director",
  SECRETARY = "Secretary",
  TAX_ADVISOR = "Tax Advisor",
  LEGAL_ADVISOR = "Legal Advisor",
  BANKER = "Banker",
  AUTHORIZED_AGENT = "Authorized Agent",
  AUTHORIZED_REPRESENTATIVE = "Authorized Representative",
  ACCOUNTANT = "Accountant",
  OTHER = "Other",
} ;

export const finalizeSchema = z.object({
  confirmationDeclaration: stringBoolean(),
  confirmationAssets: stringBoolean(),
  confirmationFunds: stringBoolean(),
  confirmationReports: stringBoolean(),
  confirmationTaxAdvice: stringBoolean(),
  confirmationFeeAcknowledgement: stringBoolean(),
  declarantName: nonEmptyString("Declarant name"),
  address: nonEmptyString("Address"),
  entityRelation: z.enum(Object.values(EntityRelation) as [string, ...string[]], {
    message: "Please select a relation to Entity",
  }),
  otherEntityRelation: z.string({
    required_error: "Please specify the other relation to entity",
  }).optional(),
  telephone: phoneSchema({ required: true }),
  email: z.string({ required_error: "Email is required" }).email({ message: "Invalid email address." }),

})
  .refine(data => data.confirmationDeclaration === "true", {
    message: "Required.",
    path: ["confirmationDeclaration"],
  })
  .refine(data => data.confirmationAssets === "true", {
    message: "Required.",
    path: ["confirmationAssets"],
  })
  .refine(data => data.confirmationFunds === "true", {
    message: "Required.",
    path: ["confirmationFunds"],
  })
  .refine(data => data.confirmationReports === "true", {
    message: "Required.",
    path: ["confirmationReports"],
  })
  .refine(data => data.confirmationTaxAdvice === "true", {
    message: "Required.",
    path: ["confirmationTaxAdvice"],
  })
  .refine(data => data.confirmationFeeAcknowledgement === "true", {
    message: "Required.",
    path: ["confirmationFeeAcknowledgement"],
  })
  .refine(data => !(data.entityRelation === EntityRelation.OTHER && !data.otherEntityRelation), {
    message: "Please specify the other relation to entity.",
    path: ["otherEntityRelation"],
  });

export type FinalizeSchemaType = z.infer<typeof finalizeSchema>;
