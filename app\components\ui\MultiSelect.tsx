import type { ReactNode } from "react";
import {
  Button,
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@netpro/design-system";
import { ChevronsUpDown } from "lucide-react";

export type MultiSelectOption = {
  key: string
  label: string
  checked: boolean
  disabled?: boolean
};

export type MultiSelectProps = {
  options: MultiSelectOption[]
  label?: string
  onChange: (key: string, checked: boolean) => void
  triggerLabel: string
  closeOnSelect?: boolean
};

/*
 * This component is tightly coupled to app/components/FormMultiSelect
 * if breaking changes need to be made to it consider creating a new component
 */
export function MultiSelect({ options, label, onChange, triggerLabel, closeOnSelect = true }: MultiSelectProps): ReactNode {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="sm" variant="outline" className="inline-flex justify-between gap-2 border-0 ring-1 ring-inset ring-gray-300 focus-visible:ring-primary focus-visible:ring-2 shadow-sm">
          <span className="first-letter:uppercase max-w-full pr-6 overflow-ellipsis overflow-hidden">{triggerLabel}</span>
          <ChevronsUpDown size={16} className="opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        {label && <DropdownMenuLabel>{label}</DropdownMenuLabel>}
        <DropdownMenuSeparator />
        {options.map(option => (
          <DropdownMenuCheckboxItem
            className="mr-4"
            key={option.key}
            checked={option.checked}
            disabled={option.disabled}
            onSelect={(event) => {
              if (!closeOnSelect) {
                event.preventDefault();
              }
            }}
            onCheckedChange={checked => onChange(option.key, checked)}
          >
            {option.label}
          </DropdownMenuCheckboxItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
