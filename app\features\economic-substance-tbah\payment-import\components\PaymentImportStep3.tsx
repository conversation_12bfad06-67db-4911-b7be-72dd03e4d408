import type { ReactNode } from "react";
import { <PERSON><PERSON>, CardDescription, CardHeader, CardTitle, Separator, Spinner, Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@netpro/design-system";
import { useFetcher, useNavigate } from "@remix-run/react";
import { Check, ChevronRight, TriangleAlert, X } from "lucide-react";
import { useEffect } from "react";
import { CardContainer } from "~/components/CardContainer";
import type { MarkSubmissionsAsPaidByCompanyYearResponse, MarkSubmissionsAsPaidByCompanyYearsRequestDTO } from "~/services/api-generated";
import { useImportPaymentContext } from "../hooks/use-import-payment-context";

type UpdateResponseType = {
  success: boolean
} & MarkSubmissionsAsPaidByCompanyYearResponse;

export function PaymentImportStep3(): ReactNode {
  const { tableData } = useImportPaymentContext()
  const navigate = useNavigate()
  const fetcher = useFetcher<UpdateResponseType>();
  const isSubmitting = fetcher.state === "loading" || fetcher.state === "submitting";

  useEffect(() => {
    const body: MarkSubmissionsAsPaidByCompanyYearsRequestDTO = { companyFilingYears: tableData?.data, isPaid: true }
    fetcher.submit({ data: JSON.stringify(body) }, { action: "/economic-substance/payment-management/payment-status/update", method: "post" })
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <CardContainer>
      <CardHeader className="px-0 pb-1">
        <CardTitle className="text-lg">Submitting bulk updates for Company annual fees</CardTitle>
      </CardHeader>
      <CardDescription className="pb-5">We're currently processing the payment status for the provided records. Please stay on this page while this process is runing</CardDescription>
      <div className="flex flex-col justify-center items-center border-gray-400 border rounded-lg p-10">
        {isSubmitting && (
          <>
            <Spinner className="text-primary size-10" />
            <p className="text-sm">Importing data...</p>
          </>
        )}

        {!isSubmitting && !fetcher.data?.results?.some(result => result.errorMessage) && (
          <>
            <Check className="text-green-500 size-10" />
            <p className="text-sm">The data has been imported successfully</p>
          </>
        )}
        {!isSubmitting && fetcher.data?.results?.some(result => result.errorMessage) && (
          <>
            <TriangleAlert className="text-orange-500 size-10" />
            <p className="text-sm">The data has been imported successfully but the following lines were skipped:</p>

            <Table className="mt-5">
              <TableHeader>
                <TableRow>
                  <TableHead>Company</TableHead>
                  <TableHead>Year</TableHead>
                  <TableHead>Message</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {fetcher.data?.results.map((result) => {
                  if (result.errorMessage) {
                    return (
                      <TableRow key={String(result.companyVPCode) + String(result.financialYear)}>
                        <TableCell>{result.companyVPCode}</TableCell>
                        <TableCell>{result.financialYear}</TableCell>
                        <TableCell>{result.errorMessage}</TableCell>
                      </TableRow>
                    );
                  } else {
                    return null;
                  }
                })}
              </TableBody>
            </Table>
          </>
        )}

        {!isSubmitting && !fetcher.data?.success
        && (
          <>
            <X className="text-red-500 size-10" />
            <p className="text-sm">The data import process has failed</p>
          </>
        )}
      </div>
      <Separator className="my-3" />
      <div className="flex gap-2 justify-end">
        <Button size="sm" onClick={() => navigate("/economic-substance/payments")}>
          Finish
          <ChevronRight className="size-4 ml-2" />
        </Button>
      </div>
    </CardContainer>
  );
}
