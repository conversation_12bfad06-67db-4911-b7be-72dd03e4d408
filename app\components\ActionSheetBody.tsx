import type { RemixFormProps } from "@remix-run/react/dist/components";
import type { FC, PropsWithChildren } from "react";
import { Form as NetProForm } from "@netpro/design-system";
import { Form as RemixForm, useFormAction, useSubmit } from "@remix-run/react";
import { FormProvider, type UseFormReturn } from "react-hook-form";

type Props = {
  formMethods?: UseFormReturn<any, any, any>
  remixFormProps?: RemixFormProps
} & PropsWithChildren

export const ActionSheetBody: FC<Props> = ({ formMethods, children }) => {
  const submit = useSubmit();
  const formAction = useFormAction();
  //
  if (!formMethods) {
    return <div className="h-full grid grid-rows-[auto_1fr_auto]">{children}</div>
  }

  const handleSubmit: RemixFormProps["onSubmit"] = async (event) => {
    event.preventDefault();
    const isValid = await formMethods.trigger()

    if (isValid) {
      /*
       * For whatever reason event.currentTarget doesn't work,
       * which is what the docs specify one should use
       */
      submit(event.target as any);
    }
  }

  return (
    <FormProvider {...formMethods}>
      <NetProForm {...formMethods}>
        <RemixForm onSubmit={handleSubmit} className="h-full grid grid-rows-[auto_1fr_auto]" method="post" action={formAction} noValidate>
          {children}
        </RemixForm>
      </NetProForm>
    </FormProvider>
  )
}
