import type { ReactNode } from "react";
import clsx from "clsx";

/**
 * FilterRow is a layout component that arranges its children in a responsive grid with configurable column counts.
 *
 * @param {object} props - The props for the component.
 * @param {ReactNode} props.children - The content to display within the grid.
 * @param {3 | 4 | 5 | 6} [props.cols] - The number of columns to display in the grid. Supports 3, 4, 5, or 6 columns. Defaults to 6.
 *
 * @returns {JSX.Element} A responsive grid container for arranging children elements.
 *
 * @example
 * // Example usage with 4 columns
 * import { FilterRow } from './FilterRow';
 *
 * function MyFilters() {
 *   return (
 *     <FilterRow cols={4}>
 *       <div>Filter 1</div>
 *       <div>Filter 2</div>
 *       <div>Filter 3</div>
 *       <div>Filter 4</div>
 *     </FilterRow>
 *   );
 * }
 */
export function FilterRow({ children, cols = 6 }: { children: ReactNode, cols?: 3 | 4 | 5 | 6 }) {
  return (
    <div className={clsx("grid gap-2 sm:grid-cols-2 auto-rows-auto", {
      "lg:grid-cols-3": cols === 3,
      "lg:grid-cols-4": cols === 4,
      "lg:grid-cols-5": cols === 5,
      "md:grid-cols-6": cols === 6,
    })}
    >
      {children}
    </div>
  )
}
