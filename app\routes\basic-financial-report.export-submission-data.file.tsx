import queryString from "query-string";
import { z } from "zod";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementExportSubmissionData } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, enhancedURL, setNotification, redirect }) => {
  await middleware(["auth"], request);
  const searchParams = new URLSearchParams(enhancedURL.search);
  const data = queryString.parse(searchParams.toString(), { arrayFormat: "bracket" })
  const submissionIdType = z.object({
    submissionIds: z.array(z.string()).min(1),
    location: z.string(),
  });
  const { submissionIds, location } = submissionIdType.parse(data);
  const { data: fileData, error, response } = await managementExportSubmissionData({ headers: await authHeaders(request), body: {
    submissionIds,
  } });

  if (error) {
    setNotification({ title: "Error!", message: error.exceptionMessage as string || undefined, variant: "error" });

    return redirect(location);
  }

  // Whitelist specific headers
  const whitelistedHeaders = new Headers();
  const allowedHeaders = ["Content-Type", "Content-Disposition"];
  for (const header of allowedHeaders) {
    const value = response.headers.get(header);
    if (value) {
      whitelistedHeaders.set(header, value);
    }
  }

  return new Response(fileData, {
    status: response.status,
    headers: whitelistedHeaders,
  });
}, { authorize: ["bfr.panama.submissions.export"] });
