import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementDownloadReport } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, params, setNotification, redirect }) => {
  await middleware(["auth"], request)
  const { reportId } = params
  if (!reportId) {
    // Note: Is this the correct error message? I think the file is an XLSX file, not a PDF file.
    throw new Error("The report id is required to download the PDF file")
  }

  const { data, response, error } = await managementDownloadReport({ headers: await authHeaders(request), path: { reportId } });
  // Whitelist specific headers
  const whitelistedHeaders = new Headers();
  const allowedHeaders = ["Content-Type", "Content-Disposition"];
  for (const header of allowedHeaders) {
    const value = response.headers.get(header);
    if (value) {
      whitelistedHeaders.set(header, value);
    }
  }

  if (error) {
    setNotification({ title: "Error", message: "Something unexpected happened at downloading the financial report", variant: "error" })

    return redirect("/simplified-tax-return/financial-reports")
  }

  return new Response(data, {
    status: 200,
    headers: whitelistedHeaders,
  });
}, { authorize: ["str.invoices.export", "bfr.panama.invoices.export"] });
