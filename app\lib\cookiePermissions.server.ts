import { createCookie } from "@remix-run/node";
import { getPermissions, type permissionName } from "~/services/api-generated";
import { authHeaders } from "./auth/utils/auth-headers";
import { getSessionData } from "./auth/utils/session.server";

/**
 * Creates and manages the `user-permissions` cookie, which stores the user's permissions
 * and associated userId. The cookie is HTTP-only, secure, and short-lived (10 minutes),
 * ensuring safe and limited access. Do not access this cookie directly as cookies aren't
 * very type-safe rather use the helper methods below
 */
const userPermissionCookie = createCookie("user-permissions", {
  httpOnly: true,
  maxAge: 60 * 10, // 10 minutes
  sameSite: "strict",
  secure: process.env.NODE_ENV === "production",
  secrets: [process.env.SESSION_SECRET || "user-permissions-secret"],
});

/**
 * The shape of the data stored in the `user-permissions` cookie.
 */
export type UserPermissionsCookieShape = {
  userId: string // The ID of the user associated with these permissions.
  permissions: permissionName[] // An array of permission strings assigned to the user.
};

export type SetPermissionCookieArgs = {
  data: UserPermissionsCookieShape
};

/**
 * Serializes and sets the `user-permissions` cookie with the given data.
 */
export async function setUserPermissionCookie({ data }: SetPermissionCookieArgs): Promise<string> {
  return userPermissionCookie.serialize(data);
}

type GetPermissionCookieArgs = {
  cookieHeader: string | null
};

/**
 * Parses the `user-permissions` cookie from the request's `Cookie` header. You probably don't
 * want to use this function as it doesn't ensure userPermissions rather see `getUserPermissions`
 */
export async function getUserPermissionCookie({ cookieHeader }: GetPermissionCookieArgs): Promise<UserPermissionsCookieShape | null> {
  const cookie = await userPermissionCookie.parse(cookieHeader);

  return cookie as UserPermissionsCookieShape | null;
}

type GetUserPermissionsArgs = {
  request: Request // The incoming HTTP request.
};

/**
 * Retrieves the user's permissions. If a valid cookie exists, permissions are retrieved
 * from it. Otherwise, permissions are fetched from the backend, and a new cookie is set.
 *
 * @param {GetUserPermissionsArgs} args - The HTTP request containing user session data.
 * @returns {Promise<readonly [UserPermissionsCookieShape | null, string | null]>}
 * - A tuple containing:
 *   1. The user's permissions and userId (if available).
 *   2. The serialized cookie string to be set in the response, or `null`.
 */
export async function getUserPermissions({ request }: GetUserPermissionsArgs): Promise<readonly [UserPermissionsCookieShape | null, string | null]> {
  const { userId } = await getSessionData(request);
  const permissionsInCookie = await getUserPermissionCookie({ cookieHeader: request.headers.get("Cookie") });
  let setCookie: string | null = null;

  if (!userId) {
    return [null, null] as const;
  }

  const fetchPermissions = permissionsInCookie === null || permissionsInCookie.userId !== userId || permissionsInCookie?.permissions?.length === 0;
  let permissions: UserPermissionsCookieShape | null;

  if (fetchPermissions) {
    const permissionsFromApi = await getPermissions({ headers: await authHeaders(request), path: { userId } }).then(
      ({ data }) => data?.map(({ permissionName }) => permissionName).filter(Boolean) as permissionName[] ?? [],
    );

    permissions = { userId, permissions: permissionsFromApi };

    setCookie = await setUserPermissionCookie({ data: permissions });
  } else {
    permissions = permissionsInCookie;
  }

  return [permissions, setCookie] as const;
}

/**
 * Destroys the `user-permissions` cookie by serializing it with a `null` value.
 *
 * @returns {Promise<string>} - The serialized cookie string for deletion.
 */
export function destroyUserPermissionCookie(): Promise<string> {
  return userPermissionCookie.serialize(null);
}
