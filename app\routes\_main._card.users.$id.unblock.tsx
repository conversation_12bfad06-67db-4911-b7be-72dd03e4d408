import type { ReactNode } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { Form, redirect, useFormAction, useNavigate, useParams } from "@remix-run/react";
import * as AlertDialog from "~/components/AlertDialog";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { blockUnblockUser } from "~/services/api-generated";

export const action = makeEnhancedAction(async ({ params, request, setNotification }) => {
  const { id } = params
  await middleware(["auth"], request);
  const { error } = await blockUnblockUser({ headers: await authHeaders(request), path: { userId: id! }, body: {
    isBlocked: false,
  } })

  if (error) {
    setNotification({ title: "User has been unblocked", variant: "error" })
  } else {
    setNotification({ title: "User has been unblocked" })
  }

  return redirect(`/users/${id}`)
}, { authorize: ["users.unblock"] });

export const loader = makeEnhancedLoader(() => {
  return null
}, { authorize: ["users.unblock"] })

export default function UserBlock(): ReactNode {
  const params = useParams();
  const navigate = useNavigate()
  const formAction = useFormAction();

  return (
    <AlertDialog.Root open onOpenChange={() => navigate(`/users/${params.id}`)}>
      <AlertDialog.Portal>
        <AlertDialog.Overlay />
        <AlertDialog.Content>
          <AlertDialog.Title>
            Are you sure you want to unblock this user?
          </AlertDialog.Title>
          <AlertDialog.Description>
            Unblocking the user will regrant them access to the application.
          </AlertDialog.Description>
          <AlertDialog.Footer>
            <AlertDialog.Cancel asChild>
              <Button onClick={() => navigate("/users/")} variant="outline" size="sm">
                Cancel
              </Button>
            </AlertDialog.Cancel>
            <Form action={formAction} method="POST">
              <Button type="submit" size="sm">Unblock</Button>
            </Form>
          </AlertDialog.Footer>
        </AlertDialog.Content>
      </AlertDialog.Portal>
    </AlertDialog.Root>
  )
}
