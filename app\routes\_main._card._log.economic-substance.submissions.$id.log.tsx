import type { ReactNode } from "react";
import { Outlet } from "@remix-run/react";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";

export const handle = {
  breadcrumb: {
    label: "Submissions",
    to: "/economic-substance/submissions",
  },
  title: "View History logs",
  /*
   * This is the path to return to when the back button is clicked
   * It's required to define this path to return to the correct page
   */
  returnTo: {
    to: "/economic-substance/submissions/",
    label: "Back to Submissions",
  },
}

export const loader = makeEnhancedLoader(async ({ request }) => {
  await middleware(["auth"], request);

  return null;
}, {
  authorize: ["es.bahamas.submissions.view"],
});

export default function Layout(): ReactNode {
  return <Outlet />
}
