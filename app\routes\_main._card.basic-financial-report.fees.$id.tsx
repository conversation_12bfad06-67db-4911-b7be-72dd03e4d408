import type { ReactNode } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@netpro/design-system";
import { useParams } from "@remix-run/react";
import { useForm } from "react-hook-form";
import { ActionSheetBody } from "~/components/ActionSheetBody";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { FormInput } from "~/components/FormInput";
import { feeSchema, type FeeSchemaType } from "~/features/basic-financial-report/fees/schemas/fee-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { Jurisdictions } from "~/lib/utilities/jurisdictions";
import { requireActiveJurisdiction } from "~/lib/utilities/require-active-jurisdiction";
import type { SettingsDTO } from "~/services/api-generated";
import { setJurisdictionSettings } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request }) => {
  await middleware(["auth"], request);

  return null;
}, { authorize: ["companies.custom-bfr-fee.set"] });

export const action = makeEnhancedAction(async ({ request, setNotification, redirect }) => {
  await middleware(["auth"], request);
  const { jurisdiction: bfrJurisdiction } = await requireActiveJurisdiction({ request, code: Jurisdictions.PANAMA });
  const formData = await request.formData()
  const fee = Number(formData.get("fee"));
  const body: SettingsDTO = {
    feeSettings: { bfrSubmissionFee: fee },
  }
  const { error } = await setJurisdictionSettings({ headers: await authHeaders(request), path: { jurisdictionId: bfrJurisdiction.id || "" }, body })
  if (error) {
    setNotification({ title: "Failed to update the Basic Financial Report fee", variant: "error" })
  } else {
    setNotification({ title: "Updated Basic Financial Report fee successfully", variant: "success" })
  }

  return redirect("/basic-financial-report/fees")
}, { authorize: ["companies.custom-bfr-fee.set"] });

export default function FeeUpdate(): ReactNode {
  const params = useParams();
  const formMethods = useForm<FeeSchemaType>({
    defaultValues: {
      fee: !params.id || Number.isNaN(Number.parseFloat(params.id)) || params.id === "create" ? 0 : Number.parseFloat(params.id),
    },
    resolver: zodResolver(feeSchema),
  });

  return (
    <ActionSheetBody formMethods={formMethods}>
      <ActionSheetContent title="Update Fee">
        <span className="text-bold text-xl text-blue-700">Settings</span>
        <FormInput
          name="fee"
          label="Submission fee*"
          formItemProps={{ className: "w-full" }}
          inputProps={{ placeholder: "0.00", type: "number", min: 0, ...formMethods.register("fee") }}
        />
        <span className="text-sm text-gray-500">Note: This is the regular amount that is charged to all submissions for all years, unless they have a deviation on the company level</span>
      </ActionSheetContent>
      <ActionSheetFooter>
        <Button size="sm" type="submit">{params.id === "create" ? "Create" : "Update"}</Button>
      </ActionSheetFooter>
    </ActionSheetBody>
  )
}
