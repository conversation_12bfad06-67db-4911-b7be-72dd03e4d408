import type { MultiSelectProps } from "./ui/MultiSelect";
import { useState } from "react";
import { FormFieldReset } from "~/components/FormFieldReset";
import { makeFormField } from "~/lib/makeFormField";
import { MultiSelect } from "./ui/MultiSelect";

export type FormMultiSelectProps = Omit<MultiSelectProps, "onChange" | "label" | "options"> & {
  options: Omit<MultiSelectProps["options"], "disabled" >
  disabledOptions?: string[]
  closeOnSelect?: boolean
}

/**
 * FormMultiSelect is a higher-order component created with `makeFormField` that integrates a multi-select field
 * with form state management. It allows users to select multiple options, handle "Select All" functionality,
 * and dynamically update the trigger label based on the selected options.
 *
 * @template FormMultiSelectProps - Extends the `MultiSelectProps`, omitting `onChange` and `label`.
 *
 * @param {object} props - Props required by the component.
 * @param {object} props.field - The form field object with `value` and `onChange` handler.
 * @param {Array<{ key: string, label: string }>} props.options - Options available for selection. Each option has a `key` and `label`.
 * @param {string} props.triggerLabel - Default label displayed on the multi-select trigger button.
 *
 * @returns {JSX.Element} A multi-select field with form integration and a reset button.
 *
 * @example
 * // Usage Example
 * import { FormMultiSelect } from './FormMultiSelect';
 *
 * const options = [
 *   { key: 'option1', label: 'Option 1' },
 *   { key: 'option2', label: 'Option 2' },
 *   { key: 'option3', label: 'Option 3' },
 * ];
 *
 * function MyFormComponent({ field }) {
 *   return (
 *     <FormMultiSelect
 *       field={field}
 *       options={options}
 *       triggerLabel="Select Options"
 *     />
 *   );
 * }
 */
export const FormMultiSelect = makeFormField<FormMultiSelectProps>({ displayName: "FormMultiSelect", render({ field, options, triggerLabel, disabledOptions, closeOnSelect = true }) {
  const value = field.value as string[] | undefined;
  // The hooks should work just fine since makeFormField is a factory
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [initialOptions] = useState<FormMultiSelectProps["options"]>(options)
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [initialTriggerLabel] = useState(triggerLabel)
  //
  const handleChange: MultiSelectProps["onChange"] = (key, checked) => {
    if (key === "all") {
      return field.onChange(initialOptions.map(option => option.key))
    }

    if (checked) {
      return field.onChange([...(value || []), key])
    }

    const index = value!.indexOf(key)

    return field.onChange(value?.toSpliced(index, 1))
  }
  //
  const getTriggerLabel = (): string => {
    if (value?.length === 1) {
      return options.find(({ key }) => key === value[0])!.label
    }

    if (value?.length && value.length > 1) {
      return `${value?.length} selected`;
    }

    return initialTriggerLabel
  }

  return (
    <div className="w-full [&>button]:w-full relative overflow-hidden">
      <MultiSelect
        onChange={handleChange}
        triggerLabel={getTriggerLabel()}
        closeOnSelect={closeOnSelect}
        options={[{ key: "all", checked: false, label: "All" }, ...initialOptions].map(option => ({ ...option, checked: value?.includes(option.key) || false, disabled: disabledOptions?.includes(option.key) }))}
      />
      {field.value?.length > 0
      && (
        <FormFieldReset variant="select" onReset={() => field.onChange([])} />
      )}
    </div>
  )
} })
