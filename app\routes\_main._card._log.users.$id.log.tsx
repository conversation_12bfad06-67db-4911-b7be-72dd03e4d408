import type { ReactNode } from "react";
import { Outlet } from "@remix-run/react";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";

export const handle = {
  breadcrumb: {
    label: "Users",
    to: "/users",
  },
  title: "View History Logs",
  /*
   * This is the path to return to when the back button is clicked
   * It's required to define this path to return to the correct page
   */
  returnTo: {
    to: (id: string) => `/users/${id}`,
    label: "Back to User Details",
  },
}

export const loader = makeEnhancedLoader(() => {
  return null
}, { authorize: ["users.view-log"] })

export default function Layout(): ReactNode {
  return <Outlet />
}
