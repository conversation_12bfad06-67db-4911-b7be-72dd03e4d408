import type { Row } from "@tanstack/react-table";
import { <PERSON><PERSON>, Button, SelectItem } from "@netpro/design-system";
import { Link, useLoaderData, useLocation, useNavigation } from "@remix-run/react";
import { Download, Filter } from "lucide-react";
import { type ReactNode, useMemo } from "react";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormMultiSelect } from "~/components/FormMultiSelect";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import type { DetailsSchema } from "~/features/bo-directors/schemas/detailsSchema";
import { searchSchema } from "~/features/bo-directors/schemas/searchSchema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { makeMakeColumn } from "~/lib/makeMakeColumn";
import { middleware } from "~/lib/middlewares.server";
import type { BoDirDataStatus, BoDirItemDTO, BoDirItemDTOPaginatedResponse, BoDirPosition, BoDirSpecifics, ManagementListBoDirsData, ProductionOfficeType } from "~/services/api-generated";
import { managementListBoDirs } from "~/services/api-generated";

const productionOffice: Record<ProductionOfficeType, string> = {
  TBVI: "TBVI",
  THKO: "THKO",
  TNEV: "TNEV",
  TPANVG: "TPANVG",
  TCYP: "TCYP",
}
//
const position: Record<BoDirPosition, string> = {
  Director: "Director",
  BeneficialOwner: "Beneficial Owner",
}
//
const statusOptions: Record<BoDirDataStatus, string> = {
  Confirmed: "Confirmed",
  Initial: "Initial",
  PendingUpdateRequest: "Pending Update Request",
  Refreshed: "Refreshed",
  Subsequent: "Subsequent",
}
const specifics: Record<BoDirSpecifics, string> = {
  NoBoDirInformation: "No Director & BO",
  BoDirInformation: "Has Director or BO",
  MissingInformation: "Missing Information",
}
//
type SortableColumns = NonNullable<NonNullable<ManagementListBoDirsData["query"]>["SortBy"]>;
//
export const sortableColumnNames = Object.keys({
  Status: null,
  ConfirmedDate: null,
  DirectorName: null,
  DirectorType: null,
  DirectorVPCode: null,
  EntityPortalCode: null,
  LegalEntityName: null,
  MasterClientCode: null,
  OfficerType: null,
  Position: null,
  ProductionOffice: null,
  ReferralOffice: null,
  RequestUpdateDate: null,
  Specifics: null,
  VPEntityNumber: null,
} satisfies Record<SortableColumns, null>)
//
const makeColumn = makeMakeColumn<SortableColumns, BoDirItemDTO>(sortableColumnNames)
//
function useColumns() {
  const formatColDate = useFormatColDate()

  return [
    makeColumn({ header: "Related Party Name", id: "DirectorName", accessorKey: "directorName", enableSorting: true }),
    makeColumn({ header: "Related Party VP Code", id: "DirectorVPCode", accessorKey: "directorVPCode", enableSorting: true }),
    makeColumn({ header: "Type of Officer", id: "OfficerType", accessorKey: "officerType", enableSorting: true }),
    makeColumn({ header: "Individual / Corporate", id: "IsIndividual", accessorKey: "IsIndividual", cell: value => value.row.original.isIndividual ? "Individual" : "Corporate", enableSorting: true }),
    makeColumn({ header: "Entity Name", id: "LegalEntityName", accessorKey: "legalEntityName", enableSorting: true }),
    makeColumn({ header: "Entity VP Code", id: "VpEntityNumber", accessorKey: "vpEntityNumber", enableSorting: true }),
    makeColumn({ header: "Regulatory Code", id: "EntityPortalCode", accessorKey: "entityPortalCode", enableSorting: true }),
    makeColumn({ header: "Master Client Code", id: "MasterClientCode", accessorKey: "masterClientCode", enableSorting: true }),
    makeColumn({ header: "Production Office", id: "ProductionOffice", accessorKey: "productionOffice", enableSorting: true }),
    makeColumn({ header: "Referral Office", id: "ReferralOffice", accessorKey: "referralOffice", enableSorting: true }),
    makeColumn({ header: "Specifics", id: "Specifics", accessorKey: "specifics", enableSorting: true }),
    makeColumn({ header: "Status", id: "Status", accessorKey: "status", enableSorting: true }),
    makeColumn({ header: "Request Update Date", id: "RequestUpdateDate", accessorKey: "requestUpdateDate", cell: formatColDate("requestUpdateDate"), enableSorting: true }),
    makeColumn({ header: "Confirmed Date", id: "ConfirmedDate", accessorKey: "confirmedDate", cell: formatColDate("confirmedDate"), enableSorting: true }),
  ]
}

export const handle = {
  breadcrumb: {
    label: "Ownership & Officers",
    to: "/bo-directors/overview",
  },
  title: "Overview",
}

export const loader = makeEnhancedLoader(async ({ json, enhancedURL, request, queryString, getUserPreferences }) => {
  await middleware(["auth"], request);
  const { tablePageSize } = await getUserPreferences();
  const searchParseResult = searchSchema.safeParse(queryString);
  if (!searchParseResult.success) {
    throw new Response("Invalid search parameters", { status: 400 });
  }

  if (Object.keys(searchParseResult.data).length === 0) {
    return json({
      paginatedBoDirs: { data: [], totalItemCount: 0 } as BoDirItemDTOPaginatedResponse,
      hasEmptySearch: true,
    })
  }

  const { data: paginatedBoDirs, error } = await managementListBoDirs({ headers: await authHeaders(request), query: {
    ...searchParseResult.data,
    PageSize: tablePageSize,
    PageNumber: searchParseResult.data?.page,
    SortOrder: enhancedURL.searchParams.get("orderDirection") ?? undefined,
    SortBy: enhancedURL.searchParams.get("order") ?? undefined as any,
  } })

  if (error) {
    // Unhandled API error
    console.error("Error fetching Ownership & Officers", error);
    throw new Response("Currently unable to retrieve Ownership & Officers", { status: 412 });
  }

  return json({
    paginatedBoDirs,
    hasEmptySearch: false,
  })
}, { authorize: ["bo-dir.view", "bo-dir.search"] })

function sheetURL(row: Row<BoDirItemDTO>) {
  const map: Record<string, DetailsSchema["kind"]> = {
    BeneficialOwner: "beneficial-owner",
    Director: "director",
  }

  return `/bo-directors/overview/${row.id}/${map[row.original.position || ""]}`
}

export default function BoDirectors(): ReactNode {
  const columns = useColumns()
  const location = useLocation();
  const navigation = useNavigation();
  const { formMethods } = useFilterForm(searchSchema);
  const { paginatedBoDirs: { data: boDirs, totalItemCount }, hasEmptySearch } = useLoaderData<typeof loader>();
  //
  const specificsWatch = formMethods.watch("Specifics");
  const disabledSpecifics = useMemo((): BoDirSpecifics[] => {
    if (specificsWatch?.includes("NoBoDirInformation")) {
      return ["MissingInformation", "BoDirInformation"]
    }

    if (specificsWatch?.includes("MissingInformation") || specificsWatch?.includes("BoDirInformation")) {
      return ["NoBoDirInformation"]
    }

    return []
  }, [specificsWatch])

  return (
    <CardContainer>
      {hasEmptySearch && (
        <div className="mb-3">
          <Alert variant="info" title="No filters applied" dismissible>
            No filters have been applied. Please apply filters to search for specific Owners & Officers.
          </Alert>
        </div>
      )}
      <Form formMethods={formMethods}>
        <Authorized oneOf={["bo-dir.search"]}>
          <FilterRow cols={4}>
            <FormColumnsFilter
              label="Visible Columns"
              columns={columns}
            />
            <FormSelect
              name="ProductionOffice"
              label="Production Office"
              selectValueProps={{ placeholder: "All" }}
              options={Object.entries(productionOffice).map(([key, value]) => <SelectItem key={key} value={key}>{value}</SelectItem>)}
            />
            <FormSelect
              name="Position"
              label="Position"
              selectValueProps={{ placeholder: "All" }}
              options={Object.entries(position).map(([key, value]) => <SelectItem key={key} value={key}>{value}</SelectItem>)}
            />
            <FormMultiSelect
              name="DataStatuses"
              label="Status"
              triggerLabel="All"
              options={Object.entries(statusOptions).map(([key, label]) => ({ checked: true, key, label }))}
            />
          </FilterRow>
          <FilterRow cols={3}>
            <FormDatePicker name="ConfirmedDateFrom" label="Confirmed After" />
            <FormDatePicker name="ConfirmedDateTo" label="Confirmed Before" />
            <FormMultiSelect
              name="Specifics"
              label="Specifics"
              triggerLabel="All"
              disabledOptions={disabledSpecifics}
              options={Object.entries(specifics).map(([key, value]) => ({ checked: false, key, label: value }))}
            />
          </FilterRow>
        </Authorized>
        <FilterRow>
          <div className="col-span-full flex flex-row items-center gap-2">
            <Authorized oneOf={["bo-dir.search"]}>
              <FormSearch name="SearchTerm" formItemProps={{ className: "w-full" }} inputProps={{ placeholder: "Search by entity name, officer name, master client code, entity vp code, referral office, etc." }} />
              <Button size="sm" className="gap-1.5" type="submit">
                <Filter size={14} />
                Apply Filter(s)
              </Button>
            </Authorized>
            <Authorized oneOf={["bo-dir.export"]}>
              <Button size="sm" variant="link" className="gap-1.5" type="button" asChild>
                <Link to={`/bo-directors/download${location.search}`} reloadDocument>
                  Export as XLSX
                  <Download size={14} />
                </Link>
              </Button>
            </Authorized>
          </div>
        </FilterRow>
      </Form>

      <EnhancedTableContainer>
        <EnhancedTable
          sheetURL={sheetURL}
          returnURL="/bo-directors/overview"
          data={boDirs}
          loading={<LoadingState isLoading={navigation.state === "loading"} />}
          rowId="id"
          columns={columns}
          totalItems={totalItemCount}
          defaultOpen={/^\/bo-directors\/overview\/./.test(location.pathname)}
        />
      </EnhancedTableContainer>
    </CardContainer>
  )
}

export const ErrorBoundary = PageErrorBoundary;
