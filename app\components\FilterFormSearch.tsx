import type { FC, PropsWithChildren } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { Filter } from "lucide-react";
import { FilterRow } from "./FilterRow";
import { FormSearch } from "./FormSearch";

type Props = {
  placeholder: string
} & PropsWithChildren

export const FilterFormSearch: FC<Props> = ({ children, placeholder }) => {
  return (
    <FilterRow>
      <div className="col-span-full flex flex-row items-center gap-2">
        <FormSearch name="SearchTerm" formItemProps={{ className: "w-full" }} inputProps={{ placeholder }} />
        <Button size="sm" className="gap-1.5" type="submit">
          <Filter size={14} />
          Apply Filter(s)
        </Button>
      </div>
      {children}
    </FilterRow>
  )
}
