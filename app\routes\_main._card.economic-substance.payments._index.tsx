import { Button, Label, SelectItem } from "@netpro/design-system";
import { useLoaderData, useNavigation } from "@remix-run/react";
import { CloudUpload, Filter, Wallet } from "lucide-react";
import { type ReactNode, useState } from "react";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormColumnsFilter } from "~/components/FormColumnsFilter";
import { FormCombobox } from "~/components/FormCombobox";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormSearch } from "~/components/FormSearch";
import { FormSelect } from "~/components/FormSelect";
import { LinkButton } from "~/components/ui/buttons/LinkButton";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { UpdatePaymentStatusDialog } from "~/features/dialogs/payments/UpdatePaymentStatusDialog";
import { useEsTbahColumns } from "~/features/economic-substance-tbah/hooks/use-es-tbah-columns";
import { relevantActivityOptions } from "~/features/economic-substance-tbah/types/relevant-activities";
import { searchSchema } from "~/features/economic-substance-tbah/types/search-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type {
  ListSubmissionBahamasDTOPaginatedResponse,
  MarkSubmissionsAsPaidRequestDTO,
  SubmissionDTO,
} from "~/services/api-generated";
import { managementBahamasListSubmissionsByModule, managementMarkAsPaid } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Economic Substance",
    to: "/economic-substance/payments",
  },
  title: "Payments",
}

export const loader = makeEnhancedLoader(async ({ request, queryString, setNotification, json }) => {
  await middleware(["auth"], request);
  const { module: strModule } = await requireActiveModule({ request, key: Modules.ECONOMIC_SUBSTANCE_BAHAMAS });
  const schemaData = searchSchema.safeParse(queryString).data
  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });
  const { data: paginatedSubmissions, error } = await managementBahamasListSubmissionsByModule({ headers: await authHeaders(request), query: {
    ModuleId: strModule.id,
    PageNumber: pageNumber,
    SortOrder: orderDirection,
    SortBy: order as any,
    PageSize: pageSize,
    SubmittedAfterDate: schemaData?.submittedAfterDate,
    SubmittedBeforeDate: schemaData?.submittedBeforeDate,
    FinancialPeriodStartAt: schemaData?.financialPeriodStartAt,
    FinancialPeriodEndAt: schemaData?.financialPeriodEndAt,
    RelevantActivities: schemaData?.relevantActivities as string[] | undefined,
    IsPaid: schemaData?.isPaid === "true" ? true : schemaData?.isPaid === "false" ? false : undefined,
    ShowSubmitted: schemaData?.showSubmitted === "true" ? true : schemaData?.showSubmitted === "false" ? false : undefined,
    AllowReopen: schemaData?.allowReopen === "true" ? true : schemaData?.allowReopen === "false" ? false : undefined,
    GeneralSearchTerm: schemaData?.search,
    IsDeleted: false,
  } })

  if (error) {
    if (error.code === 8) {
      // Invalid sorting response
      setNotification({
        title: "Invalid data sorting",
        message: `Cannot sort the table by colum ${order}.`,
      })

      return json({
        paginatedSubmissions: { data: [], totalItemCount: 0 } as ListSubmissionBahamasDTOPaginatedResponse,
      })
    }

    // Unhandled API error
    console.error("Error fetching submissions", error);
    throw new Response("Currently unable to retrieve Economic Substance Bahamas submissions", { status: 412 });
  }

  return json({
    paginatedSubmissions,
  });
}, { authorize: ["es.bahamas.submissions.view-paid"] })

export const action = makeEnhancedAction(async ({ request, setNotification, json }) => {
  const formData = await request.formData()
  const data = formData.get("data") as string
  const body = JSON.parse(data) as MarkSubmissionsAsPaidRequestDTO
  const { error } = await managementMarkAsPaid({ headers: await authHeaders(request), body })

  if (error) {
    setNotification({ title: "Failed at updating payment status", variant: "error" })
  } else {
    const numberOfSubmissions = body.submissionIds?.length
    if (numberOfSubmissions) {
      let alertMessage = ""

      if (numberOfSubmissions === 1) {
        alertMessage = "1 company"
      }

      if (numberOfSubmissions > 1) {
        alertMessage = `${numberOfSubmissions} companies`
      }

      setNotification({
        title: "Payment status updated",
        message: `Payment status is updated for ${alertMessage}`,
        variant: "success",
      })
    }
  }

  return json(null)
}, { authorize: ["es.bahamas.submissions.mark-paid"] })

export default function EconomicSubstanceBahamasPaymentsLayout(): ReactNode {
  const { paginatedSubmissions: { data: submissions, totalItemCount } } = useLoaderData<typeof loader>();
  const navigation = useNavigation();
  const { formMethods } = useFilterForm(searchSchema);
  const { columns: submissionColumns } = useEsTbahColumns("payments");
  const [rowSelection, setRowSelection] = useState({})
  const selectedSubmissionsId = Object.keys(rowSelection)
  const [open, setOpen] = useState(false)
  const isDisabledUpdateStatusButton = Object.entries(rowSelection).length === 0

  return (
    <>
      <CardContainer>
        <Authorized oneOf={["es.bahamas.submissions.search"]}>
          <Form formMethods={formMethods}>
            <FilterRow cols={5}>
              <FormColumnsFilter label="Visible Columns" columns={submissionColumns} />
              <FormDatePicker name="submittedAfterDate" label="Submitted After" />
              <FormDatePicker name="submittedBeforeDate" label="Submitted Before" />
              <FormDatePicker name="financialPeriodStartAt" label="Financial Period Starts Date" />
              <FormDatePicker name="financialPeriodEndAt" label="Financial Period End Date" />
            </FilterRow>
            <FilterRow cols={5}>
              <FormSelect
                name="isPaid"
                label="Status"
                selectValueProps={{ placeholder: "All" }}
                options={[{ key: "true", value: "Paid" }, { key: "false", value: "Unpaid" }].map(p => (
                  <SelectItem key={p.key} value={p.key}>{p.value}</SelectItem>
                ))}
              />
              <div className="grid col-span-4 grid-cols-4 gap-2">
                <div className="col-span-2">
                  <FormCombobox
                    name="relevantActivities"
                    label="Relevant Activities"
                    options={relevantActivityOptions}
                    comboboxProps={{
                      placeholder: "Select an activity",
                      searchText: "Search...",
                      noResultsText: "No activities found.",
                      multiple: true,
                    }}
                  />
                </div>
                <div className="flex sm:gap-2 gap-0 sm:flex-row flex-col">
                  <Authorized oneOf={["es.bahamas.submissions.mark-paid"]}>
                    <div className="flex gap-2">
                      <div className="flex flex-col justify-end gap-2">
                        <Label>Payment Status</Label>
                        <Button
                          size="sm"
                          type="button"
                          className="gap-1.5"
                          disabled={isDisabledUpdateStatusButton}
                          onClick={() => setOpen(true)}
                        >
                          <Wallet size={14} />
                          Update Status
                        </Button>
                      </div>
                    </div>
                  </Authorized>
                  <Authorized oneOf={["es.bahamas.payments.import"]}>
                    <div className="flex flex-col justify-end gap-2">
                      <Label>Payment Records</Label>
                      <LinkButton linkProps={{ to: "/economic-substance/payments/import" }} buttonProps={{ size: "sm" }}>
                        <CloudUpload className="size-4 mr-2" />
                        Upload File
                      </LinkButton>
                    </div>
                  </Authorized>
                </div>
              </div>
            </FilterRow>
            <FilterRow>
              <div className="col-span-full flex flex-row items-center gap-2">
                <FormSearch
                  name="search"
                  formItemProps={{ className: "w-full" }}
                  inputProps={{ placeholder: "Search entity name, master client, referral office, etc." }}
                />
                <Button size="sm" className="gap-1.5" type="submit">
                  <Filter size={14} />
                  Apply Filter(s)
                </Button>
              </div>
            </FilterRow>
          </Form>
        </Authorized>
        <EnhancedTableContainer>
          <EnhancedTable
            data={submissions}
            loading={<LoadingState isLoading={navigation.state === "loading"} />}
            rowId="id"
            columns={submissionColumns}
            totalItems={totalItemCount}
            reactTableOptions={{
              state: {
                rowSelection,
              },
              onRowSelectionChange: setRowSelection,
              enableSorting: true,
            }}
          />
        </EnhancedTableContainer>
      </CardContainer>
      {Boolean(totalItemCount) && (
        <UpdatePaymentStatusDialog
          open={open}
          onOpenChange={setOpen}
          submissions={submissions as SubmissionDTO[]}
          selectedSubmissions={selectedSubmissionsId}
        />
      )}
    </>
  );
}
