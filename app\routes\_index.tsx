import type { LoaderFunctionArgs, MetaArgs, MetaFunction, TypedResponse } from "@remix-run/node";
import { redirect } from "@remix-run/react";
import { redirectToLogin } from "~/lib/auth/utils/authentication.server";
import { defaultMeta } from "~/lib/config";
import { middleware } from "~/lib/middlewares.server";

export const meta: MetaFunction = ({ error }: MetaArgs) => [
  ...defaultMeta(error ? "Error" : undefined),
];

export async function loader({ request }: LoaderFunctionArgs): Promise<TypedResponse> {
  try {
    await middleware(["auth"], request);

    // User is already logged in, redirect to dashboard
    return redirect("/dashboard");
  } catch (ResponseError) {
    // User is not logged in, redirect to login
    return redirectToLogin(request);
  }
}
