import { But<PERSON> } from "@netpro/design-system";
import { type FC, type PropsWithChildren, useContext } from "react";
import { ActionSheetContext } from "./ActionSheetContext";

type Props = {} & PropsWithChildren

export const ActionSheetFooter: FC<Props> = ({ children }) => {
  const { closeSheet } = useContext(ActionSheetContext)

  return (
    <div className="border-t border-t-gray-200 py-2 flex gap-x-2 justify-end">
      <Button onClick={closeSheet} type="button" variant="outline">Close</Button>
      {children}
    </div>
  )
}
