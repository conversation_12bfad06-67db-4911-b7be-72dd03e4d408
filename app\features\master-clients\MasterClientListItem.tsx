import type { ReactNode } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@netpro/design-system";
import { <PERSON> } from "@remix-run/react";
import { ChevronRight, ScrollText } from "lucide-react";
import { withClassName } from "~/lib/with-class-name";
import type { MasterClientDTO } from "~/services/api-generated";

type Props = {
  client: MasterClientDTO
  onClick?: () => void
}

const UserListHeader = withClassName("h3", "text-xs font-bold ")
const UserList = withClassName("p", "line-clamp-1 text-muted-foreground text-sm font-semibold transition-colors duration-150 group-hover:text-blue-800 group-hover:font-semibold")

export function MasterClientListItem({ client, onClick }: Props): ReactNode {
  const managers = client.masterClientManagers?.map(manager => manager.email?.toLowerCase())?.join(", ") || "No managers registered"
  const users = client.masterClientUsers?.filter(({ firstName, lastName }) => firstName !== "System" && lastName !== "Inbox").map(({ email }) => email?.toLowerCase()).join(", ") || "No owners registered"

  return (
    <div className="flex flex-row flex-grow-0 p-2 py-3 gap-2 bg-white rounded-lg cursor-pointer hover:border-blue-500 transition-colors duration-150 border-2 border-transparent group" onClick={onClick}>
      <div className="flex-grow">
        <h2 className="text-xl font-bold transition-colors duration-150 group-hover:text-blue-700 flex items-center gap-2">
          {client.code || "Unknown code"}
          {" "}
          {client.masterClientUsers && client.masterClientUsers.length > 0 && <Badge variant="secondary" className="bg-teal-100 text-teal-900">{`${client.masterClientUsers.length} owners`}</Badge>}
          {client.masterClientManagers && client.masterClientManagers.length > 0 && <Badge variant="secondary">{`${client.masterClientManagers.length} managers`}</Badge>}
        </h2>
        <div className="flex flex-col gap-y-1">
          <div>
            <UserListHeader>Owners</UserListHeader>
            <UserList title={users}>
              {users}
            </UserList>
          </div>
          <div>
            <UserListHeader>Managers</UserListHeader>
            <UserList title={managers}>
              {managers}
            </UserList>
          </div>
        </div>
      </div>
      <div className="flex flex-row items-center gap-1">
        <Button asChild size="sm" variant="outline" className="self-center text-sm flex items-center justify-center gap-1.5 mr-2">
          <Link to={`/master-clients/${client.id}/log`} onClick={e => e.stopPropagation()}>
            <ScrollText size={14} className="text-blue-600" />
            <span className="text-xs font-semibold">View Log</span>
          </Link>
        </Button>
        <Button size="sm" variant="outline" className="self-center text-sm flex items-center justify-center gap-1.5 mr-2">
          <span className="text-xs font-semibold">Edit Details</span>
          <ChevronRight className="w-4 h-4 text-blue-600" />
        </Button>
      </div>
    </div>
  );
}
