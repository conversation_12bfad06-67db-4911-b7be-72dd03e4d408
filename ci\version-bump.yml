trigger:
  branches:
    include:
      - main
      - develop
  tags:
    exclude:
      - '*'

pool:
  vmImage: ubuntu-latest

variables:
  # Common variables
  azureDevOpsOrg: https://dev.azure.com/netprogroup
  azureDevOpsProject: Trident Trust - Private Client Portal
  ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/develop') }}:
    prereleaseTag: beta
    pipelineName: Management Portal - dev
    updateDevelopment: false
  ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/main') }}:
    prereleaseTag: beta
    pipelineName: Management Portal - test
    updateDevelopment: true

jobs:
  - job: BumpVersion
    displayName: 'Bump version'
    steps:
      - checkout: self
        clean: true
        persistCredentials: true
        fetchDepth: 0
        displayName: 'Checkout repository'

      - task: NodeTool@0
        inputs:
          versionSource: 'fromFile'
          versionFilePath: './.nvmrc'
          checkLatest: true
        displayName: 'Install Node.js'

      - task: Npm@1
        inputs:
          command: 'install'
          workingDir: '.'
          verbose: true
        displayName: 'Install dependencies'

      - script: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Azure Build Pipeline"
        displayName: 'Set git user'

      # Bump version with prerelease tag
      - task: Npm@1
        displayName: 'Bump version with $(prereleaseTag) tag'
        inputs:
          command: 'custom'
          workingDir: '.'
          customCommand: 'run release -- --prerelease $(prereleaseTag)'

      - script: |
          git push --follow-tags origin HEAD:$(Build.SourceBranch)
        condition: and(succeeded(), eq(variables['updateDevelopment'], 'false'))
        displayName: 'Push changes with tags'

      - script: |
          git push --follow-tags origin HEAD:develop HEAD:main
        condition: and(succeeded(), eq(variables['updateDevelopment'], 'true'))
        displayName: 'Push changes with tags and update develop'

      # Trigger pipeline with the new tag
      - script: |
          # Get the tag that was just created
          TAG=$(git describe --tags --abbrev=0)
          echo "Triggering $(pipelineName) pipeline for tag: $TAG"

          # Use Azure DevOps REST API to trigger the pipeline
          az pipelines run --name "$(pipelineName)" --branch "refs/tags/$TAG" --organization "$(azureDevOpsOrg)" --project "$(azureDevOpsProject)"
        displayName: 'Trigger $(pipelineName) pipeline'
        env:
          AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)
