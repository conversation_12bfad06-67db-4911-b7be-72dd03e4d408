parameters:
  - name: displayName
    type: string
    default: 'Build application'

steps:
  - checkout: self
    displayName: 'Checkout $(Build.SourceBranch)'
    clean: true
    persistCredentials: true

  - task: NodeTool@0
    inputs:
      versionSource: 'fromFile'
      versionFilePath: './.nvmrc'
      checkLatest: true
    displayName: 'Install Node.js'

  - task: Npm@1
    inputs:
      command: 'custom'
      workingDir: '.'
      customCommand: 'install --include=dev'
    displayName: 'Install NPM packages including dev dependencies'

  - task: Npm@1
    inputs:
      command: 'custom'
      workingDir: '.'
      customCommand: 'run build'
    displayName: 'Build the application'

  - task: Npm@1
    inputs:
      command: 'custom'
      workingDir: '.'
      customCommand: 'prune --omit=dev'
    displayName: 'Remove devDependencies from node_modules'

  - task: CopyFiles@2
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)'
      Contents: |
        node_modules/**
        build/**
        package.json
        package-lock.json
      TargetFolder: '$(Build.ArtifactStagingDirectory)'
      CleanTargetFolder: true

  - task: ArchiveFiles@2
    displayName: 'Archive $(Build.ArtifactStagingDirectory) to $(Build.ArtifactStagingDirectory)/package.zip'
    inputs:
      rootFolderOrFile: '$(Build.ArtifactStagingDirectory)'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)/package.zip'
      replaceExistingArchive: true
      verbose: true

  - task: PublishPipelineArtifact@1
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/package.zip'
      ArtifactName: 'app'
      publishLocation: 'pipeline'
