import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import type { UserPreferencesValue } from "~/components/ContextUserPreferences";
import { userPreferences } from "~/lib/cookies.server";

type Props = {
  request: LoaderFunctionArgs["request"] | ActionFunctionArgs["request"]
}
export async function getUserPreferences({ request }: Props): Promise<UserPreferencesValue> {
  const cookieHeader = request.headers.get("Cookie");

  return (await userPreferences.parse(cookieHeader)) || {}
}
