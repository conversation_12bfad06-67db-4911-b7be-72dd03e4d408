import { z } from "zod";
import { nonEmptyString, stringNumber } from "~/lib/utilities/zod-validators";

export const incomeSchema = z.object({
  totalGrossIncome: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: true }),
  netBookValuesAssets: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: true }),
  assetsDescriptionBahamas: nonEmptyString("Description"),
})

export type IncomeSchemaType = z.infer<typeof incomeSchema>

export function getIncomeDefaultValues(data: IncomeSchemaType | undefined): IncomeSchemaType {
  return {
    totalGrossIncome: data?.totalGrossIncome ?? "",
    netBookValuesAssets: data?.netBookValuesAssets ?? "",
    assetsDescriptionBahamas: data?.assetsDescriptionBahamas ?? "",
  };
}
