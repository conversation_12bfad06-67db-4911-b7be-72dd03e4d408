import type { ReactNode } from "react";
import { useLoaderData } from "@remix-run/react";
import Page from "~/components/pages/Page";
import PageContent from "~/components/pages/PageContent";
import PageHeader from "~/components/pages/PageHeader";
import { Logo } from "~/components/ui/branding/Logo";
import { SubmissionStatusNamesEnum } from "~/features/submissions/utilities/submission-status";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";

export function SummaryPage({ children }: { children: ReactNode }): ReactNode {
  const { entityDetails: { status } } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const childrenReturn = () => {
    if (status === SubmissionStatusNamesEnum.DRAFT || status === SubmissionStatusNamesEnum.TEMPORAL) {
      return (
        <div className="relative">
          <div className="absolute text-blue-500/30 font-extrabold transform -rotate-45 text-[250px] top-32 -right-10">DRAFT</div>
          {children}
        </div>
      )
    }

    return (
      <div className="flex flex-col gap-4 font-inter">
        {children}
      </div>
    )
  }

  return (
    <Page>
      <PageHeader>
        <div className="flex justify-end w-full">
          <Logo className="h-8" />
        </div>
      </PageHeader>
      <PageContent>
        {childrenReturn()}
      </PageContent>
    </Page>
  );
}
