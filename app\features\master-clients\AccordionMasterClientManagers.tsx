import type { ReactNode } from "react";
import {
  <PERSON><PERSON>,
  Separator,
} from "@netpro/design-system";
import { Plus, X } from "lucide-react";
import { useState } from "react";
import { Authorized } from "~/components/Authorized";

import { AccordionContent, AccordionItem, AccordionTrigger } from "~/components/ui/accordion";
import {
  DialogRemoveMasterClientManager,
} from "~/features/master-clients/DialogRemoveMasterClientManager";
import { FormAddManager } from "~/features/master-clients/FormAddManager";
import { MasterClientUserListItem } from "~/features/master-clients/MasterClientUserListItem";
import type { ListUserDTO } from "~/services/api-generated";

type Props = {
  value: string
  managers: ListUserDTO[]
}
export function AccordionMasterClientManagers({ managers, value }: Props): ReactNode {
  const [addManager, setAddManager] = useState(false);

  return (
    <AccordionItem value={value} className="border-b-0">
      <AccordionTrigger onClick={() => setAddManager(false)} className="text-blue-700 text-xl focus-visible:ring-0 outline-0">
        {`Trident Managers ${managers.length > 0 ? `(${managers.length})` : ""}`}
      </AccordionTrigger>
      <AccordionContent className="w-auto">
        {managers.length === 0
          ? (
              <span>No Trident managers assigned.</span>
            )
          : (
              <ul className="">
                {managers.map(user => (
                  <MasterClientUserListItem label={user?.email || "Email address unknown"} key={user.id} editable>
                    <DialogRemoveMasterClientManager user={user} />
                  </MasterClientUserListItem>
                ))}
              </ul>
            )}
        <Separator className="my-4" />

        <Authorized oneOf={["masterclients.trident-users.add"]}>
          <div className="flex flex-col gap-4">
            {!addManager && (
              <Button type="button" size="sm" variant="outline" className="gap-1 place-self-end" onClick={() => setAddManager(true)}>
                <Plus className="text-blue-600" size={16} />
                Assign Manager
              </Button>
            )}
            {addManager && (
              <>
                <FormAddManager closeAddManager={() => setAddManager(false)} />
                <Button type="button" size="sm" variant="outline" className="gap-1 place-self-end" onClick={() => setAddManager(false)}>
                  <X className="text-blue-600" size={16} />
                  Cancel
                </Button>
              </>
            )}
          </div>
        </Authorized>
      </AccordionContent>
    </AccordionItem>
  )
}
