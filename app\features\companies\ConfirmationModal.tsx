import type { JS<PERSON>, <PERSON>actNode } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, DialogHeader } from "@netpro/design-system"
import { Check, Info } from "lucide-react"

type ConfirmationModalProps = {
  isOpen: boolean
  onClose: () => void
  title?: string
  text?: string
  formId: string
  children?: ReactNode
}

export function ConfirmationModal({
  isOpen,
  onClose,
  title = "Are you sure?",
  text = "Clicking 'confirm' will approve and enable this company",
  formId,
  children,
}: ConfirmationModalProps): JSX.Element {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="[&>button]:hidden">
        <DialogHeader className="flex flex-col items-center gap-4">
          <div>
            <div className="flex items-center justify-start space-x-2">
              <Info className="w-6 h-6 text-blue-500" />
              <h3 className="font-bold text-lg">{title}</h3>
            </div>
            <p className="text-gray-500 whitespace-nowrap ml-8 my-3">{text}</p>
          </div>
        </DialogHeader>
        {children}
        <DialogFooter className="sm:justify-end gap-2">
          <Button variant="outline" onClick={onClose} type="button">
            Cancel
          </Button>
          <Button type="submit" form={formId}>
            Confirm
            <Check className="w-4 h-4 ml-2" />
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
