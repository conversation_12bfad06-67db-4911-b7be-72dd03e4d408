import { z } from "zod";

/**
 * You will probably want to use this if your form contains a <FormSelect> component which can be reset
 * resetting the form turn the value into empty string which is not a valid part of the enum, .nullable CAN be used
 * however that will allow the entry to be part of the validated schema, which something you don't want when storing
 * the form values in searchParams
 */
export function optionalEnumWithEmpty<const T extends string[]>(values: T) {
  return z.preprocess(
    (value) => {
      if (value === "") {
        return undefined;
      }

      return value;
    },
    z.enum(values as unknown as [T[number], ...T]).optional(),
  );
}
