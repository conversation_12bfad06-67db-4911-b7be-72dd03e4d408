import type { ForwardRefExoticComponent, ReactNode, SVGProps } from "react";

type Props = {
  count: number
  entityName: string
  icon: ForwardRefExoticComponent<Omit<SVGProps<SVGSVGElement>, "ref">>
}

export function MigrationStatusCard({ count, entityName, icon: Icon }: Props): ReactNode {
  return (
    <div className="flex items-center space-x-3 py-2.5 pl-4 pr-8 ring-1 ring-gray-200 rounded-md">
      <div className="flex items-start h-full">
        <Icon className="size-6 text-blue-600" />
      </div>
      <div>
        <p className="text-xs font-semibold text-gray-700">{entityName}</p>
        <p className="text-xl font-semibold text-gray-700">{count.toLocaleString()}</p>
      </div>
    </div>
  );
}
