import type { ApiClientResponse } from "~/features/master-clients/types/generic";

export async function translateErrorResponse(errorResponse: unknown): Promise<ApiClientResponse<null>> {
  if (errorResponse instanceof Response) {
    return {
      result: null,
      error: {
        code: errorResponse.status,
        message: String(await errorResponse.text()),
      },
    }
  }

  return {
    result: null,
    error: {
      code: 1337,
      message: "Server responded with an error message.",
    },
  }
}
