import { But<PERSON> } from "@netpro/design-system";
import { Form, redirect, useFormAction, useNavigate, useParams } from "@remix-run/react";
import { Check } from "lucide-react";
import * as AlertDialog from "~/components/AlertDialog";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementReopenSubmission } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request }) => {
  await middleware(["auth"], request);

  return null;
}, { authorize: ["bfr.panama.submissions.reset"] });

export const action = makeEnhancedAction(async ({ params, request, setNotification }) => {
  const { id } = params
  await middleware(["auth"], request);
  const { error } = await managementReopenSubmission({ headers: await authHeaders(request), path: { submissionId: id! } });

  if (error) {
    setNotification({ title: "Failed to reopen submission.", variant: "error" })
  } else {
    setNotification({ title: "Submission is reopen for selected company." })
  }

  return redirect(`/basic-financial-report/submissions/${id}`)
}, { authorize: ["bfr.panama.submissions.reset"] });

export default function ResetSubmissionStatus(): JSX.Element {
  const params = useParams();
  const navigate = useNavigate()
  const formAction = useFormAction();

  return (
    <AlertDialog.Root open onOpenChange={() => navigate(`/basic-financial-report/submissions/${params.id}`)}>
      <AlertDialog.Portal>
        <AlertDialog.Overlay />
        <AlertDialog.Content>
          <AlertDialog.Title>
            Are you sure?
          </AlertDialog.Title>
          <AlertDialog.Description>
            Clicking 'confirm' will re-open the submission for the client to amend changes.
          </AlertDialog.Description>
          <AlertDialog.Footer>
            <AlertDialog.Cancel asChild>
              <Button onClick={() => navigate("/basic-financial-report/submissions")} variant="outline" size="sm">
                Cancel
              </Button>
            </AlertDialog.Cancel>
            <Form action={formAction} method="POST">
              <Button type="submit" size="sm">
                Confirm
                <Check className="size-4 ml-2" />
              </Button>
            </Form>
          </AlertDialog.Footer>
        </AlertDialog.Content>
      </AlertDialog.Portal>
    </AlertDialog.Root>
  )
}
