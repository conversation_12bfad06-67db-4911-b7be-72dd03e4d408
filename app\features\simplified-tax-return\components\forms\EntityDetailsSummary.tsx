import type { FC, ReactNode } from "react";
import { useSubmission } from "~/features/submissions/context/use-submission";
import { useFormatDate } from "~/lib/hooks/useFormatDate";

export type EntitySubmissionDetails = {
  submittedAt?: string
  status: string
  financialYear: number
  companyName: string
  companyCode: string
  masterClientCode: string
  isPaid?: boolean
};

const PaymentStatus: FC<{ isPaid?: boolean }> = ({ isPaid }) => {
  const className = isPaid ? "text-green-500" : "text-red-500"

  return (
    <span className={className}>
      (
      {isPaid ? "PAID" : "UNPAID"}
      )
    </span>
  )
}

export function EntityDetailsSummary({ entityDetails }: { entityDetails: EntitySubmissionDetails }): ReactNode {
  const formatDate = useFormatDate()
  const { submissionData } = useSubmission();

  return (
    <section id="entity-details-section" className="pt-2">
      <h2 className="text-lg font-semibold">Entity Details</h2>
      <div className="flex justify-between py-2 pl-2 pr-12 border-2 border-blue-600">
        <div className="flex flex-col gap-1">
          <p className="flex gap-1">
            Entity Name:
            <span className="font-semibold">{submissionData["legal-entity-data"].name}</span>
          </p>
          <p className="flex gap-1">
            VP Code:
            <span className="font-semibold">{submissionData["legal-entity-data"].incorporationNr}</span>
          </p>
          <p className="flex gap-1 mb-6">
            Master Client Code:
            <span className="font-semibold">{submissionData["legal-entity-data"].masterClientCode}</span>
          </p>
          <p>
            Registered Agent:
          </p>
          <p className="font-semibold">Trident Trust Company (Nevis) Limited</p>
        </div>

        <div className="flex flex-col gap-4">
          <div className="flex flex-col justify-center items-center py-4">
            <p className="font-semibold">Reporting Year:</p>
            <span className="font-semibold">{entityDetails.financialYear}</span>
          </div>

          <p className="flex gap-1">
            Submitted Date:
            <span className="font-semibold">{entityDetails.submittedAt ? formatDate(entityDetails.submittedAt) : ""}</span>
          </p>

          <p className="flex gap-1">
            Status:
            <span className="font-semibold">
              {entityDetails.status.toUpperCase()}
              {" "}
              <PaymentStatus isPaid={entityDetails.isPaid} />
            </span>
          </p>
        </div>
      </div>
    </section>
  );
}
