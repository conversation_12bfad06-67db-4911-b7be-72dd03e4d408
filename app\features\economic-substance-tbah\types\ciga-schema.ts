import { z } from "zod";
import { nonEmptyString, preprocessArray, stringBoolean, stringNumber } from "~/lib/utilities/zod-validators";

export enum ActivityEnum {
  NO_CIGA = "0.1",
  OTHER_PLEASE_SPECIFY = "0.2",
  RAISING_FUNDS_MANAGING_RISK = "1.1",
  TAKING_HEDGING_POSITIONS = "1.2",
  PROVIDING_LOANS_CREDIT_SERVICES = "1.3",
  MANAGING_REGULATORY_CAPITAL = "1.4",
  PREPARING_REGULATORY_REPORTS = "1.5",
  PREDICTING_AND_CALCULATING_RISK = "2.1",
  INSURING_AGAINST_RISK = "2.2",
  PROVIDING_INSURANCE_SERVICES = "2.3",
  DECISIONS_ON_INVESTMENTS = "3.1",
  CALCULATING_RISK_AND_RESERVES = "3.2",
  DECISIONS_ON_CURRENCY_FLUCTUATIONS = "3.3",
  PREPARING_REGULATORY_REPORTS_GOVERNMENT = "3.4",
  AGREEING_FUNDING_TERMS = "4.1",
  IDENTIFYING_AND_ACQUIRING_ASSETS = "4.2",
  SETTING_TERMS_AND_DURATION = "4.3",
  MONITORING_AND_REVISING_AGREEMENTS = "4.4",
  MANAGING_RISKS = "4.5",
  TAKING_MANAGEMENT_DECISIONS = "5.1",
  INCURRING_EXPENDITURES_FOR_AFFILIATES = "5.2",
  COORDINATING_GROUP_ACTIVITIES = "5.3",
  MANAGING_CREW = "6.1",
  MAINTAINING_SHIPS = "6.2",
  OVERSEEING_DELIVERIES = "6.3",
  DETERMINING_GOODS_ORDERING = "6.4",
  ORGANISING_VOYAGES = "6.5",
  BUSINESS_WITH_PATENTS = "8.1",
  BUSINESS_WITH_TRADEMARKS = "8.2",
  TRANSPORTING_AND_STORING_GOODS = "9.1",
  MANAGING_STOCKS = "9.2",
  TAKING_ORDERS = "9.3",
  PROVIDING_CONSULTING_SERVICES = "9.4",
}

// CIGA Activities
export const cigaActivitySchema = z.object({
  description: nonEmptyString("Description"),
  otherActivity: nonEmptyString("This field", true),
}).refine(data => !(data.description === ActivityEnum.OTHER_PLEASE_SPECIFY && !data.otherActivity), {
  message: "Required",
  path: ["otherActivity"],
})

export type CigaActivitySchemaType = z.infer<typeof cigaActivitySchema>

// CIGA Outsourcing Providers

export const cigaOutsourcingProvidersSchema = z.object({
  entityName: nonEmptyString("Entity name"),
  detailsOfResources: nonEmptyString("Detais of resources"),
  numberOfStaff: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: false }),
  monitoringAndControl: stringBoolean(),
  physicalAddress: nonEmptyString("Physical address"),
  monitoringControlExplanation: nonEmptyString("Monitoring control explanation"),
})

export type CigaOutsourcingProvidersSchemaType = z.infer<typeof cigaOutsourcingProvidersSchema>

export const cigaSchema = z.object({
  hasCiga: stringBoolean(),
  activities: preprocessArray(z.array(cigaActivitySchema)),
  isCigaOutsourced: stringBoolean(),
  cigaOutsourcingProportion: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0.0, lessThan: 101, allowDecimal: true, optional: true }),
  bahamasOutsourcingExpenditure: stringNumber({ invalidTypeMessage: "An amount is required", greaterThan: 0, allowDecimal: true, optional: true }),
  outsourcingProviders: preprocessArray(z.array(cigaOutsourcingProvidersSchema)),
})
  .refine(data => !(data.hasCiga === "true"
    && (!data.activities || data.activities.length === 0)), {
    message: "One item is required at least",
    path: ["activities", 0],
  })
  .refine(data => !(data.isCigaOutsourced === "true"
    && Number(data.bahamasOutsourcingExpenditure) === 0), {
    message: "Required",
    path: ["bahamasOutsourcingExpenditure"],
  })
  .refine(data => !(data.isCigaOutsourced === "true"
    && Number(data.cigaOutsourcingProportion) === 0), {
    message: "Required",
    path: ["cigaOutsourcingProportion"],
  })
  .refine(data => !(data.isCigaOutsourced === "true"
    && (!data.outsourcingProviders || data.outsourcingProviders.length === 0)), {
    message: "One item is required at least",
    path: ["outsourcingProviders", 0],
  })

export type CigaSchema = z.infer<typeof cigaSchema>

export function getCigaDefaultValues(data: CigaSchema | undefined): CigaSchema {
  return {
    hasCiga: (data?.hasCiga ?? undefined) as "true" | "false", // bypass to set as undefined the first status
    activities: data?.activities ?? [],
    isCigaOutsourced: (data?.isCigaOutsourced ?? undefined) as "true" | "false",
    cigaOutsourcingProportion: data?.cigaOutsourcingProportion ?? "",
    bahamasOutsourcingExpenditure: data?.bahamasOutsourcingExpenditure ?? "",
    outsourcingProviders: data?.outsourcingProviders ?? [],
  };
}
