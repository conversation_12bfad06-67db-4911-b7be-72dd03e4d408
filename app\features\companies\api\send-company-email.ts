import type { ApiClientResponse, ErrorResponse } from "~/features/master-clients/types/generic";
import { client } from "~/lib/api-client.server";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export type SendCompanyEmailParams = {
  companyId: string
  data: {
    recipientEmailAddress: string
    subject: string
    body: string
    legalEntityId: string
  }
}

export async function sendCompanyEmail({
  companyId,
  userId,
  data,
}: SendCompanyEmailParams & ClientRequestHeaders): Promise<ApiClientResponse<Response | ErrorResponse>> {
  return client.post(
    `/management/companies/${companyId}/email`,
    userId,
    data,
  );
}
