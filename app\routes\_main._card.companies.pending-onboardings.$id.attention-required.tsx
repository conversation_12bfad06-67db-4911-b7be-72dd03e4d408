import type { FieldErrors } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Footer, Di<PERSON>Header, <PERSON><PERSON>Title, DialogTrigger, Input, Label, notify, Spinner, Textarea } from "@netpro/design-system";
import { Form, useFetcher, useParams } from "@remix-run/react";
import { Mail } from "lucide-react";
import { useEffect } from "react";
import { getValidatedFormData, useRemixForm } from "remix-hook-form";
import { sendCompanyEmail } from "~/features/companies/api/send-company-email";
import type { AttentionRequiredCompanySchemaType } from "~/features/companies/schemas/attention-required-company";
import { attentionRequiredCompanySchema } from "~/features/companies/schemas/attention-required-company";
import { usePreserveQueryNavigate } from "~/lib/hooks/usePreserveQueryNavigate";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";

export const loader = makeEnhancedLoader(async ({ request }) => {
  await middleware(["auth"], request);

  return null;
}, {
  authorize: ["companies.onboarding.access"],
});

export const action = makeEnhancedAction(async ({ request, json, params }) => {
  const { userId } = await middleware(["auth"], request);
  const { id } = params;

  if (!id) {
    throw new Response("Not Found", { status: 404 });
  }

  const { errors, data } = await getValidatedFormData<AttentionRequiredCompanySchemaType>(request, zodResolver(attentionRequiredCompanySchema));

  if (errors) {
    return json({ errors });
  }

  const EMAIL_SUBJECT = "Attention required for onboarding a new company";

  try {
    // TODO: Implement proper error handling using { data, error }  pattern
    await sendCompanyEmail({
      companyId: id,
      userId,
      data: {
        recipientEmailAddress: data.recipientEmail,
        subject: EMAIL_SUBJECT,
        body: data.assistanceRequestComments,
        legalEntityId: id,
      },
    });
  } catch (error) {
    if (error instanceof Response) {
      return json({ success: false, errors: { recipientEmail: { message: await error.text() } } });
    }

    return json({ success: false });
  }

  return json({ success: true });
}, {
  authorize: ["companies.onboarding.access"],
});

export default function AttentionRequired(): JSX.Element {
  const params = useParams();
  const navigate = usePreserveQueryNavigate();
  const fetcher = useFetcher<{
    success: boolean
    errors?: FieldErrors<AttentionRequiredCompanySchemaType>
  }>();
  const { handleSubmit, formState: { errors, isSubmitting }, register } = useRemixForm<AttentionRequiredCompanySchemaType>({
    mode: "onSubmit",
    resolver: zodResolver(attentionRequiredCompanySchema),
    submitConfig: {
      action: `/companies/pending-onboardings/${params.id}/attention-required`,
    },
    fetcher,
  });

  useEffect(() => {
    if (fetcher.data) {
      if (fetcher.data.success) {
        navigate("/companies/pending-onboardings");
        notify({ message: "Email sent successfully", variant: "success" });
      } else {
        notify({ message: "Failed to send email", variant: "error" });
      }
    }
  }, [fetcher.data, navigate]);

  return (
    <Dialog open onOpenChange={() => navigate(`/companies/pending-onboardings/${params.id}`)}>
      <DialogTrigger />
      <DialogContent>
        <Form method="POST" onSubmit={handleSubmit} noValidate className="grid gap-4">
          <DialogHeader>
            <div className="flex items-center gap-2 mb-2">
              <Mail className="text-blue-700" />
              <DialogTitle>Send Email</DialogTitle>
            </div>
          </DialogHeader>
          <div className="space-y-1">
            <Label className="text-xs font-semibold text-muted-foreground">Recipient Email *</Label>
            <Input
              {...register("recipientEmail")}
              disabled={isSubmitting}
              type="email"
              invalid={Boolean(errors?.recipientEmail)}
              placeholder="<EMAIL>"
            />
            {errors?.recipientEmail && <p className="text-red-600">{errors.recipientEmail.message}</p>}
          </div>
          <div className="space-y-1">
            <Textarea
              {...register("assistanceRequestComments")}
              disabled={isSubmitting}
              invalid={Boolean(errors?.assistanceRequestComments)}
              placeholder="Enter your message here..."
              className="min-h-[100px]"
            />
            {errors?.assistanceRequestComments && <p className="text-red-600">{errors.assistanceRequestComments.message}</p>}
          </div>
          <DialogFooter className="flex gap-2">
            <Button
              disabled={isSubmitting}
              variant="outline"
              type="button"
              onClick={() => navigate(`/companies/pending-onboardings/${params.id}`)}
            >
              Cancel
            </Button>
            <Button disabled={isSubmitting} type="submit">
              {isSubmitting ? <Spinner className="size-4 mx-0 text-white" /> : "Request update"}
            </Button>
          </DialogFooter>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
