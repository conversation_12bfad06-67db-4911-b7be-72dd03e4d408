import { client } from "~/lib/api-client.server";
import type { ClientRequestHeaders } from "~/lib/types/client-request-headers";

export type JurisdictionSettingsKey = "fees" | "documents" | "str-late-payment-fees";

export type GetJurisdictionSettingsParams = {
  jurisdictionId: string
  key: JurisdictionSettingsKey
};

export type JurisdictionSettingsResponse = Record<string, unknown>;

export function getJurisdictionSettings({
  jurisdictionId,
  key,
  userId,
}: GetJurisdictionSettingsParams & ClientRequestHeaders): Promise<JurisdictionSettingsResponse> {
  return client.get<JurisdictionSettingsResponse>(
    `/management/jurisdictions/${jurisdictionId}/settings/${key}`,
    userId,
  );
}
