import type { ReactNode } from "react";
import { But<PERSON>, cn } from "@netpro/design-system";
import { Link, Outlet, useLoaderData, useLocation, useNavigate, useParams } from "@remix-run/react";
import { parseISO } from "date-fns";
import { FileDown, ScrollIcon } from "lucide-react";
import { useContext } from "react";
import { useForm } from "react-hook-form";
import { ActionSheetActionRow } from "~/components/ActionSheetActionRow";
import { ActionSheetBody } from "~/components/ActionSheetBody";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetDescriptionList } from "~/components/ActionSheetDescriptionList";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { ActionSheetSection } from "~/components/ActionSheetSection";
import { Authorized } from "~/components/Authorized";
import { ContextUser } from "~/components/ContextUser";
import { LinkButton } from "~/components/ui/buttons/LinkButton";
import { ReadableSubmissionStatusNames, SubmissionStatusNamesEnum } from "~/features/submissions/utilities/submission-status";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFormatDate } from "~/lib/hooks/useFormatDate";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { managementGetSubmission } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ request, params, json, setNotification, redirect }) => {
  await middleware(["auth"], request);

  const submissionResponse = await managementGetSubmission({ headers: await authHeaders(request), path: { submissionId: params.id! } })
  if (!submissionResponse.data) {
    setNotification({ title: "The requested submission could not be found", variant: "error" })

    return redirect(`/economic-substance/submissions?${new URLSearchParams({ hideDeleted: "true" })}`)
  }

  return json({ submission: submissionResponse.data })
}, { authorize: ["es.bahamas.submissions.view"] });

export default function SubmissionDetail(): ReactNode {
  const params = useParams();
  const navigate = useNavigate();
  const formMethods = useForm();
  const formatDate = useFormatDate()
  const { submission } = useLoaderData<typeof loader>();
  const { pathname } = useLocation()
  const { permissions } = useContext(ContextUser);

  return (
    <>
      <ActionSheetBody formMethods={formMethods}>
        <ActionSheetContent title="Submission Details">
          <ActionSheetSection title="Details" collapsible>
            <ActionSheetDescriptionList
              data={submission}
              headers={[
                ["legalEntityName", "Entity Name"],
                ["legalEntityCode", "Regulatory Code"],
                ["masterClientCode", "Master Client Code"],
                ["status", "Status", ({ status }): string => status ? ReadableSubmissionStatusNames[status] : ""],
                ["isDeleted", "Deleted?", ({ isDeleted }): string => isDeleted ? "Yes" : "No"],
                ["createdAt", "Created Date", ({ createdAt }): string => createdAt ? formatDate(parseISO(createdAt)) : ""],
                ["submittedAt", "Submitted Date", ({ submittedAt }): string => submittedAt ? formatDate(parseISO(submittedAt)) : ""],
                ["paymentReceivedAt", "Paid Date", ({ paymentReceivedAt }): string => paymentReceivedAt ? formatDate(parseISO(paymentReceivedAt)) : ""],
                ["financialYear", "Financial Year"],
                ["legalEntityReferralOffice", "Referral Office"],
              ]}
            />
          </ActionSheetSection>
          <Authorized oneOf={["es.bahamas.submissions.reset", "es.bahamas.rfi-request.view", "es.bahamas.rfi-request.cancel", "es.bahamas.rfi-request.start"]}>
            <ActionSheetSection title="Actions">
              <ActionSheetActionRow label="Reset Submission Status">
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  onClick={() => navigate(`/economic-substance/submissions/${params.id}/reset-to-saved`)}
                  disabled={submission.status !== SubmissionStatusNamesEnum.SUBMITTED}
                >
                  <span className="text-xs font-semibold">Reset to Saved</span>
                </Button>
              </ActionSheetActionRow>
              <ActionSheetActionRow label="Request For Information">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/economic-substance/submissions/${params.id}/rfi`)}
                  disabled={submission.status !== SubmissionStatusNamesEnum.SUBMITTED || !permissions?.includes("es.bahamas.rfi-request.start")}
                >
                  <span className="text-xs font-semibold">{submission.status === SubmissionStatusNamesEnum.INFORMATION_REQUESTED ? "RFI Sent" : "Start RFI"}</span>
                </Button>
              </ActionSheetActionRow>
              <ActionSheetActionRow label="Update Financial Period">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/economic-substance/submissions/${params.id}/change-financial-period`)}
                  disabled={submission.status !== SubmissionStatusNamesEnum.SUBMITTED}
                >
                  <span className="text-xs font-semibold">Update Period</span>
                </Button>
              </ActionSheetActionRow>
              <ActionSheetActionRow label="History">
                <LinkButton
                  linkProps={{ to: `/economic-substance/submissions/${params.id}/log` }}
                  buttonProps={{
                    variant: "outline",
                    size: "sm",
                    type: "button",
                  }}
                >
                  <ScrollIcon className="size-4 mr-2 text-blue-500" />
                  <span className="text-xs">View Log</span>
                </LinkButton>
              </ActionSheetActionRow>
            </ActionSheetSection>
          </Authorized>
          <ActionSheetSection title="Documents">
            <div className="flex-row space-y-2">
              {submission.status !== "Draft" && (
                <>
                  <div>
                    <Link
                      to={`/economic-substance/submissions/${params.id}/summary`}
                      className={cn("flex", !submission.submittedAt ? "pointer-events-none select-none" : "")}
                      target="_blank"
                    >
                      <Button
                        className="flex items-center"
                        type="button"
                        size="sm"
                        disabled={!submission.submittedAt}
                      >
                        <FileDown size={14} className="mr-2" />
                        <span className="text-xs font-semibold">Download Submission</span>
                      </Button>
                    </Link>
                  </div>

                  <div>
                    <LinkButton
                      linkProps={{
                        to: {
                          pathname: `/download/submissions/${submission.id}/documents`,
                          search: `?location=${pathname}`,
                        },
                        reloadDocument: true,
                      }}
                      buttonProps={{
                        disabled: submission.status !== SubmissionStatusNamesEnum.SUBMITTED,
                        size: "sm",
                        type: "button",
                      }}
                    >
                      <FileDown size={14} className="mr-2" />
                      <span className="text-xs">Download supported documents</span>
                    </LinkButton>
                  </div>
                </>
              )}
              <div>
                <Link
                  to={`/invoices/${submission.invoiceId}/file`}
                  className={cn("flex", !submission.invoiceId ? "pointer-events-none select-none" : "")}
                  target="_blank"
                >
                  <Button
                    className="flex items-center"
                    type="button"
                    size="sm"
                    disabled={!submission.invoiceId}
                  >
                    <FileDown size={14} className="mr-2" />
                    <span className="text-xs font-semibold">Download Invoice</span>
                  </Button>
                </Link>
              </div>
            </div>
          </ActionSheetSection>
        </ActionSheetContent>
        <ActionSheetFooter />
      </ActionSheetBody>
      <Outlet />
    </>
  )
}
