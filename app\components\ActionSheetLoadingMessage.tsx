import type { ReactNode } from "react";
import { Spin<PERSON> } from "@netpro/design-system";
import React from "react";

export function ActionSheetLoadingMessage({ children, message = "Loading ..." }: {
  children?: ReactNode
  message?: string
}): ReactNode {
  return (
    <span className="ml-1 inline-flex gap-1 items-center text-sm text-muted-foreground">
      <Spinner className="mr-0 size-4 text-primary" />
      {children ?? message}
    </span>
  )
}
