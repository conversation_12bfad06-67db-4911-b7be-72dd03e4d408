import type { ReactNode } from "react";
import { cn, RadioGroup } from "@netpro/design-system";
import { makeFormField } from "~/lib/makeFormField";

type Props = {
  radioGroupProps?: React.ComponentProps<typeof RadioGroup>
  children: ReactNode
}

export const FormRadioGroup = makeFormField<Props>({ displayName: "FormRadioGroup", render: ({ field, fieldState, radioGroupProps, children }) => {
  return (
    <RadioGroup
      invalid={!!fieldState.error}
      onValueChange={field.onChange}
      {...field}
      {...radioGroupProps}
      className={cn("flex flex-row space-x-2", radioGroupProps?.className)}
    >
      {children}
    </RadioGroup>
  )
} })
