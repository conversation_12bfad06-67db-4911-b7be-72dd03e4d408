import { useLoaderData } from "@remix-run/react";
import { Fragment } from "react/jsx-runtime";
import type { DirectionManagementSchemaType } from "~/features/economic-substance-tbah/types/direction-management-schema";
import type { PageSlug } from "~/features/economic-substance-tbah/utilities/form-pages";
import { transformBooleanStringToLabel } from "~/features/economic-substance-tbah/utilities/summary";
import type { EconomicSubstanceSummaryLoader } from "~/routes/_pdf.economic-substance.submissions.$id.summary";
import { SummaryTable } from "../table/SummaryTable";
import { SummaryTableData } from "../table/SummaryTableData";
import { SummaryTableRow } from "../table/SummaryTableRow";

export function DirectionManagement({ page }: { page: PageSlug }) {
  const { submissionData } = useLoaderData<EconomicSubstanceSummaryLoader>()
  const { isDirectedAndManagedInBahamas, numberOfMeetings, numberOfMeetingsInBahamas, quorumDirectors, quorumPhysicallyPresent, areMinutesKeptInBah<PERSON><PERSON>, directors } = submissionData[page] as DirectionManagementSchemaType

  return (
    <div className="space-y-5">
      <h2 className="text-blue-500 font-thin mb-4 text-lg">4. Direction and Management</h2>
      <SummaryTable>
        <tbody>
          <SummaryTableRow>
            <SummaryTableData>
              Is the activity directed and managed in the Bahamas?
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(isDirectedAndManagedInBahamas)}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Number of board meetings the entity held during the financial period with
              relation to this activity.
            </SummaryTableData>
            <SummaryTableData>{numberOfMeetings}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Number of board meetings the entity held during the financial period with
              relation to this activity in the Bahamas
            </SummaryTableData>
            <SummaryTableData>{numberOfMeetingsInBahamas}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Quorum for board meetings.
            </SummaryTableData>
            <SummaryTableData>{quorumDirectors}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Of these board meetings held in the Bahamas, was the quorum of
              directors physically present in the Bahamas?
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(quorumPhysicallyPresent)}</SummaryTableData>
          </SummaryTableRow>
          <SummaryTableRow>
            <SummaryTableData>
              Are the minutes of all board meetings kept in the Bahamas?
            </SummaryTableData>
            <SummaryTableData>{transformBooleanStringToLabel(areMinutesKeptInBahamas)}</SummaryTableData>
          </SummaryTableRow>
        </tbody>
      </SummaryTable>
      <h2 className="text-blue-700 font-bold mb-4">Directors</h2>
      <SummaryTable>
        <tbody>
          {directors.map(d => (
            <Fragment key={`${d.name}-${d.qualification}`}>
              <SummaryTableRow className="border-b-0">
                <SummaryTableData>
                  <div className="flex gap-2">
                    <span>Director Full Name:</span>
                    <p className="font-semibold">
                      {d.name}
                    </p>
                  </div>
                </SummaryTableData>
                <SummaryTableData>
                  <div className="flex gap-2">
                    <span>Bahamas Residency:</span>
                    <p className="font-semibold">
                      {transformBooleanStringToLabel(d.isResidentInBahamas)}
                    </p>
                  </div>
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow className="border-b-0">
                <SummaryTableData>
                  <div className="flex gap-2">
                    <span>Relation to entity:</span>
                    <p className="font-semibold">
                      {d.relationToEntity}
                    </p>
                  </div>
                </SummaryTableData>
                <SummaryTableData>
                  <div className="flex gap-2">
                    <span>Meetings attended:</span>
                    <p className="font-semibold">
                      {transformBooleanStringToLabel(d.meetingsAttended)}
                    </p>
                  </div>
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow className="border-b-0">
                <SummaryTableData>
                  <div className="flex gap-2">
                    <span>Number of meetings attended:</span>
                    <p className="font-semibold">
                      {d.meetingNumber?.length}
                    </p>
                  </div>
                </SummaryTableData>
                <SummaryTableData>
                  <div className="flex gap-2">
                    <span>Physically presented in the Bahamas:</span>
                    <p className="font-semibold">
                      {transformBooleanStringToLabel(d.physicallyPresentInBahamas)}
                    </p>
                  </div>
                </SummaryTableData>
              </SummaryTableRow>
              <SummaryTableRow>
                <SummaryTableData>
                  <div className="flex gap-2">
                    <span>Qualification:</span>
                    <p className="font-semibold">
                      {d.qualification}
                    </p>
                  </div>
                </SummaryTableData>
                <SummaryTableData>
                  <div className="flex gap-2">
                    <span>Years of relevant experience:</span>
                    <p className="font-semibold">
                      {d.yearsOfExperience}
                    </p>
                  </div>
                </SummaryTableData>
              </SummaryTableRow>
            </Fragment>
          ))}
        </tbody>
      </SummaryTable>
    </div>
  )
}
