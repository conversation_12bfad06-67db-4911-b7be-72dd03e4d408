import { z } from "zod";

export function nonEmptyString(fieldName: string, optional: boolean = false): z.ZodEffects<z.ZodString, string, string> {
  return z.string().refine((val) => {
    if (optional && (val.trim() === "" || val === undefined || val === null)) {
      return true; // If optional and empty or undefined, skip validation
    }

    return val.trim() !== ""; // If not optional, validate it's not an empty string
  }, {
    message: `${fieldName} is required.`,
  });
}

type NonNullDateMessageArgs = {
  message: string
  fieldName?: never
}

type NonNullDateFieldNameArgs = {
  message?: never
  fieldName: string
}

type NonNullDateArgs = NonNullDateFieldNameArgs | NonNullDateMessageArgs | string

export function nonNullDate(args: NonNullDateArgs) {
  const getMessage = (): string => {
    if (typeof args === "string") {
      return `${args} is required.`
    }

    if (args.message != null) {
      return args.message
    }

    return `${args.fieldName} is required.`
  }

  return z
    .string()
    .refine((date) => {
      const parsed = new Date(date);

      return !Number.isNaN(parsed.getTime());
    }, "Invalid date")
    .optional()
    .refine(val => val !== null && val !== undefined && val !== "", {
      message: getMessage(),
    })
}

/**
 * Validates a string is in YYYY-MM-DD format
 */
export const dateString = z
  .string()
  .refine((date) => {
    const parsed = new Date(date);

    return !Number.isNaN(parsed.getTime());
  }, "Invalid date")
  .optional();

/**
 * Optional date string validator for filter forms
 */
export const optionalDateString = z
  .string()
  .refine((date) => {
    const parsed = new Date(date);

    return !Number.isNaN(parsed.getTime());
  }, "Invalid date")
  .optional();

export function stringBoolean(): z.ZodEnum<["true", "false"]> {
  return z.enum(["true", "false"]);
}

export function stringNumber({
  invalidTypeMessage = "An amount is required.",
  allowDecimal = true,
  greaterThan = undefined, // Optional boundary
  lessThan = undefined, // Optional boundary for less than
  optional = false, //  Parameter to handle optional values
}: {
  invalidTypeMessage?: string
  allowDecimal?: boolean
  greaterThan?: number | undefined
  lessThan?: number | undefined
  optional?: boolean // Parameter to indicate if the field is optional
} = {}): z.ZodEffects<z.ZodString, string> {
  const greaterThanMessage = `An amount is required and must be greater than ${greaterThan}.`
  const lessThanMessage = `An amount is required and must be less than ${lessThan}.`;
  const schema = z
    .string()
    .refine((value) => {
      if (optional && (value === "" || value === undefined || value === null)) {
        return true; // If optional and empty or undefined, skip validation
      }

      return value === "" || !Number.isNaN(Number(value)); // If not optional, ensure it's not empty and is a number
    }, {
      message: invalidTypeMessage,
    })
    .refine((value) => {
      if (optional && (value === "" || value === undefined)) {
        return true; // If optional and empty or undefined, skip validation
      }

      const numberValue = Number(value);
      if (greaterThan !== undefined && numberValue <= greaterThan) {
        return false;
      }

      return true;
    }, {
      message: greaterThan !== undefined
        ? greaterThanMessage
        : "Please enter a valid amount.",
    })
    .refine(
      (value) => {
        if (optional && (value === "" || value === undefined)) {
          return true; // If optional and empty or undefined, skip validation
        }

        const numberValue = Number(value);
        if (lessThan !== undefined && numberValue >= lessThan) {
          return false;
        }

        return true;
      },
      {
        message: lessThan !== undefined
          ? lessThanMessage
          : "Please enter a valid amount.",
      },
    )
    .refine((value) => {
      if (optional && (value === "" || value === undefined)) {
        return true; // If optional and empty or undefined, skip validation
      }

      const numberValue = Number(value);
      if (!allowDecimal && !Number.isInteger(numberValue)) {
        return false;
      }

      return true;
    }, { message: "Please enter a whole number. Decimals are not allowed." });

  return schema as unknown as z.ZodEffects<z.ZodString, string>;
}

/**
 * Preprocesses an array input specifically for serialized data from APIs.
 *
 * @warning IMPORTANT: Use ONLY with serialized data where empty arrays are returned as empty strings.
 *
 * This function is designed to handle scenarios where:
 * - An empty array is serialized and returned as an empty string from an API
 *
 * DO NOT use this for:
 * - Regular form inputs where empty arrays should remain empty arrays
 * - Client-side data entry where the array is already in the correct format
 *
 * @param schema The Zod schema for array elements
 * @returns A Zod schema for preprocessed arrays
 */
export function preprocessArray<T extends z.ZodTypeAny>(schema: z.ZodArray<T>) {
  return z.preprocess((val) => {
    // Convert empty string to empty array
    if (val === "" || val === null || val === undefined) {
      return [];
    }

    // If it's already an array, return it
    if (Array.isArray(val)) {
      return val;
    }

    // Fallback to empty array
    return [];
  }, schema);
}
