import { NetProConfig } from "@netpro/eslint-config";

export default NetProConfig({}, {
  ignores: ["app/services/api-generated", "ci/**", ".vscode/**", "README.md"],
}, {
  files: ["app/**/*.ts", "app/**/*.tsx"],
  rules: {
    "ts/explicit-function-return-type": "off",
    "no-restricted-imports": [
      "warn",
      {
        paths: [
          {
            name: "date-fns",
            importNames: ["format"],
            message: "Use `useFormatDate` instead of `format` from date-fns.",
          },
        ],
      },
    ],
  },
});
