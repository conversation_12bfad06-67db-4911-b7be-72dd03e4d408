import type { ReactNode } from "react";
import { Button, Label } from "@netpro/design-system";
import { useLoaderData, useNavigation } from "@remix-run/react";
import { Filter, Wallet } from "lucide-react";
import { useState } from "react";
import { Authorized } from "~/components/Authorized";
import { CardContainer } from "~/components/CardContainer";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormDatePicker } from "~/components/FormDatePicker";
import { FormSearch } from "~/components/FormSearch";
import { LoadingState } from "~/components/ui/filters/LoadingState";
import { searchSchema } from "~/features/basic-financial-report/payments/schemas/search-schema";
import { usePaymentsColumns } from "~/features/basic-financial-report/utilities/payments-columns";
import { UpdatePaymentStatusDialog } from "~/features/dialogs/payments/UpdatePaymentStatusDialog";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import { Modules } from "~/lib/utilities/modules";
import { requireActiveModule } from "~/lib/utilities/require-active-modules";
import type { MarkSubmissionsAsPaidRequestDTO, SubmissionDTO } from "~/services/api-generated";
import { managementMarkAsPaid, managementPanamaListSubmissionsByModule } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Payments",
    to: "/basic-financial-report/payments",
  },
  title: "Payments",
}

export const loader = makeEnhancedLoader(async ({ request, json, queryString }) => {
  await middleware(["auth"], request);
  const { module: strModule } = await requireActiveModule({ request, key: Modules.BASIC_FINANCIAL_REPORT });
  const schemaData = searchSchema.safeParse(queryString).data
  const { pageNumber, pageSize, order, orderDirection } = await getFilterParams({ request });
  const { data: paginatedSubmissions } = await managementPanamaListSubmissionsByModule({ headers: await authHeaders(request), query: {
    ModuleId: strModule.id,
    PageNumber: pageNumber,
    GeneralSearchTerm: schemaData?.search,
    SortOrder: orderDirection,
    SortBy: order as any,
    PageSize: pageSize,
    IsPaid: false,
    SubmittedAfterDate: schemaData?.submittedAfter,
    SubmittedBeforeDate: schemaData?.submittedBefore,
    IsDeleted: false,
  } })

  if (!paginatedSubmissions) {
    throw new Response("Pagination submission not found", { status: 404 })
  }

  return json({
    paginatedSubmissions,
  });
}, { authorize: ["bfr.panama.submissions.view-paid"] })

export const action = makeEnhancedAction(async ({ request, setNotification, json }) => {
  const formData = await request.formData()
  const data = formData.get("data") as string
  const body = JSON.parse(data) as MarkSubmissionsAsPaidRequestDTO
  const { error } = await managementMarkAsPaid({ headers: await authHeaders(request), body })

  if (error) {
    setNotification({ title: "Failed at updating payment status", variant: "error" })
  } else {
    const numberOfSubmissions = body.submissionIds?.length
    if (numberOfSubmissions) {
      let alertMessage = ""

      if (numberOfSubmissions === 1) {
        alertMessage = "1 company"
      }

      if (numberOfSubmissions > 1) {
        alertMessage = `${numberOfSubmissions} companies`
      }

      setNotification({ title: "Payment status updated", message: `Payment status is updated for ${alertMessage}`, variant: "success" })
    }
  }

  return json(null)
}, { authorize: ["bfr.panama.submissions.mark-paid"] })

export default function BasicFinancialReportPaymentsManagement(): ReactNode {
  const paymentsColumns = usePaymentsColumns()
  const { paginatedSubmissions: { data: submissions, totalItemCount } } = useLoaderData<typeof loader>();
  const [open, setOpen] = useState(false)
  const navigation = useNavigation();
  const [rowSelection, setRowSelection] = useState({})
  const selectedSubmissionsId = Object.keys(rowSelection)
  const isDisabledUpdateStatusButton = Object.entries(rowSelection).length === 0
  const { formMethods } = useFilterForm(searchSchema)

  return (
    <CardContainer>
      <Form formMethods={formMethods}>
        <FilterRow cols={5}>
          <FormDatePicker
            name="submittedAfter"
            label="Submitted After"
          />
          <FormDatePicker
            name="submittedBefore"
            label="Submitted Before"
          />
          <Authorized oneOf={["bfr.panama.submissions.mark-paid"]}>
            <div className="flex gap-2">
              <div className="flex flex-col justify-end gap-2">
                <Label>Payment Status</Label>
                <Button size="sm" type="button" className="gap-1.5" disabled={isDisabledUpdateStatusButton} onClick={() => setOpen(true)}>
                  <Wallet size={14} />
                  Update Status
                </Button>
              </div>
            </div>
          </Authorized>
        </FilterRow>
        <FilterRow>
          <div className="col-span-full flex flex-row items-center gap-2">
            <FormSearch
              name="search"
              formItemProps={{ className: "w-full" }}
              inputProps={{ placeholder: "Search by entity name or master client code" }}
            />
            <Button size="sm" className="gap-1.5" type="submit">
              <Filter size={14} />
              Apply Filter(s)
            </Button>
          </div>
        </FilterRow>
      </Form>
      <EnhancedTableContainer>
        <EnhancedTable
          data={submissions}
          loading={<LoadingState isLoading={navigation.state === "loading"} />}
          rowId="id"
          columns={paymentsColumns}
          totalItems={totalItemCount}
          reactTableOptions={{
            state: {
              rowSelection,
            },
            onRowSelectionChange: setRowSelection,
            enableSorting: true,
          }}
          showPagination={false}
        />
        {totalItemCount && (
          <UpdatePaymentStatusDialog
            open={open}
            onOpenChange={setOpen}
            submissions={submissions as SubmissionDTO[]}
            selectedSubmissions={selectedSubmissionsId}
          />
        )}
      </EnhancedTableContainer>
    </CardContainer>
  );
}
