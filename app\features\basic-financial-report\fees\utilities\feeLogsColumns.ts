import { createColumnHelper } from "@tanstack/react-table";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import type { ActivityLogItemDTO } from "~/services/api-generated";

const columnHelper = createColumnHelper<ActivityLogItemDTO>();

export const feeLogsColumns = []
export function useFeeLogsColumns() {
  const formatDateColDate = useFormatColDate()
  const feeLogsColumns = [
    columnHelper.display({
      id: "Date",
      header: "Date",
      cell: formatDateColDate("actionDate", { fallback: "N/A", timezone: "Panama" }),
    }),
    columnHelper.display({
      id: "shortDescription",
      header: "Description",
      cell: props => props.row.original?.shortDescription,
    }),
  ]

  return feeLogsColumns
}
