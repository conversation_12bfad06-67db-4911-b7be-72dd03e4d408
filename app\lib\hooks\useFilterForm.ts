import type { z, ZodSchema } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSearchParams } from "@remix-run/react";
import qs from "query-string";
import { useRemixForm } from "remix-hook-form";
import { useQueryString } from "~/hooks/use-query-string";

/**
 * useFilterForm is a custom hook for managing filter forms with integrated query string synchronization
 * and Zod schema-based validation. It uses `remix-hook-form` to handle form state and validation.
 *
 * @template T - A Zod schema defining the validation rules and default values for the form.
 *
 * @param {T} schema - The Zod schema to validate the form data and parse query parameters.
 *
 * @returns {object} An object containing `formMethods` for managing form state, validation, and submission.
 *
 * @example
 * // Example usage
 * import { useFilterForm } from './useFilterForm';
 * import { z } from 'zod';
 * import { Form } from '~/components';
 *
 * const filterSchema = z.object({
 *   name: z.string().optional(),
 *   tags: z.array(z.string()).optional(),
 * });
 *
 * function FilterComponent() {
 *   const { formMethods } = useFilterForm(filterSchema);
 *   const { register, handleSubmit } = formMethods;
 *
 *   return (
 *     <Form formMethods={formMethods} remixFormProps={{ method: 'GET' }}>
 *       <input {...register('name')} placeholder="Filter by name" />
 *       <button type="submit">Apply Filters</button>
 *     </Form>
 *   );
 * }
 */
export function useFilterForm<T extends ZodSchema>(schema: T) {
  const queryString = useQueryString();
  const [,setSearchParams] = useSearchParams();
  const formMethods = useRemixForm<z.infer<T>>({
    mode: "onSubmit",
    stringifyAllValues: false,
    resolver: zodResolver(schema),
    defaultValues: schema.safeParse(queryString).data,
    submitHandlers: {
      onValid: (data) => {
        setSearchParams(qs.stringify(data, { arrayFormat: "bracket" }));
      },
    },
  })

  return { formMethods }
}
