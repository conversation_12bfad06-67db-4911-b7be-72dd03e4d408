import type { ReactNode } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { Alert, Button, Collapsible, CollapsibleContent, SelectItem, Tooltip, TooltipContent, TooltipTrigger } from "@netpro/design-system";
import { Link, Outlet, useFetcher, useLoaderData, useParams } from "@remix-run/react";
import { parseISO } from "date-fns";
import { CircleAlert, ScrollText } from "lucide-react";
import { useState } from "react";
import { useRemixForm } from "remix-hook-form";
import { ActionSheetActionRow } from "~/components/ActionSheetActionRow";
import { ActionSheetBody } from "~/components/ActionSheetBody";
import { ActionSheetContent } from "~/components/ActionSheetContent";
import { ActionSheetDescriptionList } from "~/components/ActionSheetDescriptionList";
import { ActionSheetFooter } from "~/components/ActionSheetFooter";
import { ActionSheetSection } from "~/components/ActionSheetSection";
import { Authorized } from "~/components/Authorized";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";
import { Form } from "~/components/Form";
import { FormInput } from "~/components/FormInput";
import { FormSelect } from "~/components/FormSelect";
import { FormSwitch } from "~/components/FormSwitch";
import { ConfirmationModal } from "~/features/companies/ConfirmationModal";
import type { EditPendingOnboardingsSchemaType } from "~/features/companies/schemas/edit-pending-onboardings";
import { editPendingOnboardingsSchema } from "~/features/companies/schemas/edit-pending-onboardings";
import { useUserHasPermission } from "~/hooks/use-user-has-permission";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFormatDate } from "~/lib/hooks/useFormatDate";
import { usePreserveQueryNavigate } from "~/lib/hooks/usePreserveQueryNavigate";
import { makeEnhancedAction } from "~/lib/makeEnhancedAction.server";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { Jurisdictions } from "~/lib/utilities/jurisdictions";
import { Modules } from "~/lib/utilities/modules";
import type { CompanyDTO, CompanyModuleDTO, ListCompanyModulesDTO, SettingsDTO } from "~/services/api-generated";
import { approveCompany, getCompanyById, getCompanyModules, getCompanySettings, setCompanyModules, setCompanySettings } from "~/services/api-generated";

export const loader = makeEnhancedLoader(async ({ params, request, json }) => {
  await middleware(["auth"], request);
  const { id: companyId } = params;

  if (!companyId) {
    throw new Response("Not Found", { status: 404 });
  }

  const company = await getCompanyById({ headers: await authHeaders(request), path: { companyId } })
    .then(res => res.data as CompanyDTO);
  const companyModules = await getCompanyModules({ headers: await authHeaders(request), path: { companyId } })
    .then(res => res.data as ListCompanyModulesDTO);
  const companySettings = await getCompanySettings({ headers: await authHeaders(request), path: { companyId } }).then(res => res.data)

  if (!company || !companyModules) {
    throw new Response("Not Found", { status: 404 });
  }

  return json({
    company,
    companyModules,
    companySettings,
  })
}, { authorize: ["companies.onboarding.access"] });

export const action = makeEnhancedAction(async ({ request, params, redirect, setNotification }) => {
  await middleware(["auth"], request);
  const { id: companyId } = params;

  if (!companyId) {
    throw new Response("Not Found", { status: 404 });
  }

  // Get the current modules first
  const companyModules = await getCompanyModules({ headers: await authHeaders(request), path: { companyId } })
    .then(res => res.data as ListCompanyModulesDTO);

  if (!companyModules) {
    throw new Response("Modules not found", { status: 404 });
  }

  const findModuleByKey = (key: string) => (companyModules.modules as CompanyModuleDTO[]).find(m => m.key === key);
  // TODO: Should be validated by a ZOD schema
  const formData = await request.formData();
  const data = JSON.parse(formData.get("data") as string) as EditPendingOnboardingsSchemaType
  const { strApproval, bfrApproval, boDirApproval, esBahamasApproval, bfrModuleEnabled, boDirModuleEnabled, strModuleEnabled, esBahamasModuleEnabled, submissionYear, bfrFee } = data
  const moduleUpdates = [];

  // Add Simplified Tax Return module if present
  if (strApproval !== undefined) {
    const strModule = findModuleByKey(Modules.SIMPLIFIED_TAX_RETURN);
    if (strModule) {
      moduleUpdates.push({
        id: strModule.id,
        isEnabled: strModuleEnabled,
        isApproved: strApproval,
      });
    }
  }

  // Add Ownership & Officers module if present
  if (boDirApproval !== undefined) {
    const boModule = findModuleByKey(Modules.BO_DIRECTORS);
    if (boModule) {
      moduleUpdates.push({
        id: boModule.id,
        isEnabled: boDirModuleEnabled,
        isApproved: boDirApproval,
      });
    }
  }

  // Add Basic Financial Report module if present
  if (bfrApproval !== undefined) {
    const bfrModule = findModuleByKey(Modules.BASIC_FINANCIAL_REPORT);
    if (bfrModule) {
      moduleUpdates.push({
        id: bfrModule.id,
        isEnabled: bfrModuleEnabled,
        isApproved: bfrApproval,
      });
    }
  }

  // Add Economic Substance Bahamas module if present
  if (esBahamasApproval !== undefined) {
    const esBahamasModule = findModuleByKey(Modules.ECONOMIC_SUBSTANCE_BAHAMAS);
    if (esBahamasModule) {
      moduleUpdates.push({
        id: esBahamasModule.id,
        isEnabled: esBahamasModuleEnabled,
        isApproved: esBahamasApproval,
      });
    }
  }

  const moduleUpdateResult = await setCompanyModules({
    headers: await authHeaders(request),
    path: {
      companyId,
    },
    body: {
      modules: moduleUpdates,
    },
  });

  if (!moduleUpdateResult || "error" in moduleUpdateResult) {
    throw new Response("Failed to update company modules", { status: 400 });
  }

  const companySettingsBody: SettingsDTO = {}
  if (submissionYear !== undefined) {
    companySettingsBody.submissionSettings = {
      firstSubmissionYear: Number(submissionYear),
    }
  }

  if (bfrFee !== undefined) {
    const fee = bfrModuleEnabled ? bfrFee : undefined
    companySettingsBody.feeSettings = {
      bfrSubmissionFee: fee,
    }
  }

  if (companySettingsBody && Object.values(companySettingsBody).length > 0) {
    const setSettingsResult = await setCompanySettings({ headers: await authHeaders(request), path: { companyId }, body: companySettingsBody })
    if (!setSettingsResult || "error" in setSettingsResult) {
      throw new Response("Failed to update company settings", { status: 400 });
    }
  }

  if ((strApproval || bfrApproval || esBahamasApproval) && boDirApproval) {
    const approveResult = await approveCompany({ headers: await authHeaders(request), path: {
      companyId,
    } });

    setNotification({ title: "Company approved successfully", variant: "success" });
    if (!approveResult || "error" in approveResult) {
      throw new Response("Failed to approve company", { status: 400 });
    }
  } else {
    setNotification({ title: "Company updated successfully", variant: "success" });
  }

  return redirect("/companies/pending-onboardings");
}, { authorize: ["companies.onboarding.approve"] });

const FORM_ID = "pending-onboardings-form"
export default function EditCompanyRoute(): ReactNode {
  const formatDate = useFormatDate()
  const { company, companyModules, companySettings } = useLoaderData<typeof loader>();
  const modules = companyModules.modules as CompanyModuleDTO[];
  const strModule = modules.find((m: any) => m.key === Modules.SIMPLIFIED_TAX_RETURN);
  const bfrModule = modules.find((m: any) => m.key === Modules.BASIC_FINANCIAL_REPORT);
  const boDirModule = modules.find((m: any) => m.key === Modules.BO_DIRECTORS);
  const esBahamasModule = modules.find((m: any) => m.key === Modules.ECONOMIC_SUBSTANCE_BAHAMAS);
  const isNevis = company.jurisdictionName === Jurisdictions.NEVIS
  const isPanama = company.jurisdictionName === Jurisdictions.PANAMA
  const isBahamas = company.jurisdictionName === Jurisdictions.BAHAMAS
  const navigate = usePreserveQueryNavigate();
  const params = useParams();
  const fetcher = useFetcher();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const formMethods = useRemixForm<EditPendingOnboardingsSchemaType>({
    mode: "onSubmit",
    resolver: zodResolver(editPendingOnboardingsSchema),
    submitConfig: {
      action: `/companies/overview/${params.id}/edit`,
      method: "POST",
    },
    defaultValues: {
      strApproval: strModule?.isApproved ?? false,
      boDirApproval: boDirModule?.isApproved ?? false,
      bfrApproval: bfrModule?.isApproved ?? false,
      esBahamasApproval: esBahamasModule?.isApproved ?? false,
      strModuleEnabled: strModule?.isEnabled ?? false,
      boDirModuleEnabled: boDirModule?.isEnabled ?? false,
      bfrModuleEnabled: bfrModule?.isEnabled ?? false,
      esBahamasModuleEnabled: esBahamasModule?.isEnabled ?? false,
      submissionYear: companySettings?.submissionSettings?.firstSubmissionYear == null ? "" : String(companySettings?.submissionSettings?.firstSubmissionYear),
      bfrFee: companySettings?.feeSettings?.bfrSubmissionFee || 0,
    },
    submitHandlers: {
      onValid: onSubmit,
    },
  });
  const hasApprovePermission = useUserHasPermission({ oneOf: ["companies.onboarding.approve"] })
  const { formState: { isSubmitting }, watch, handleSubmit } = formMethods;
  async function onSubmit(data: EditPendingOnboardingsSchemaType) {
    const { strModuleEnabled, boDirModuleEnabled, strApproval, boDirApproval, submissionYear, bfrApproval, bfrModuleEnabled, bfrFee, esBahamasApproval, esBahamasModuleEnabled } = data
    const body = {
      strModuleEnabled,
      boDirModuleEnabled,
      strApproval,
      boDirApproval,
      submissionYear,
      bfrApproval,
      bfrModuleEnabled,
      esBahamasApproval,
      esBahamasModuleEnabled,
      bfrFee,
    }

    fetcher.submit({ data: JSON.stringify(body) }, {
      method: "POST",
      action: `/companies/pending-onboardings/${params.id}`,
    });
  }

  // Generate years from 2019 to current year
  const submissionYears = Array.from({ length: new Date().getFullYear() + 1 - 2019 }, (_, i) => 2019 + i);
  const displayedYears = submissionYears?.filter(year => company?.incorporationDate ? year >= parseISO(company.incorporationDate).getFullYear() : true) ?? [];
  const strModuleEnabledWatch = watch("strModuleEnabled")
  const bfrModuleEnabledWatch = watch("bfrModuleEnabled")
  const boDirModuleEnabledWatch = watch("boDirModuleEnabled")
  const esBahamasModuleEnabledWatch = watch("esBahamasModuleEnabled")
  const strApprovalWatch = watch("strApproval")
  const boDirApprovalWatch = watch("boDirApproval")
  const bfrApprovalWatch = watch("bfrApproval")
  const esBahamasApprovalWatch = watch("esBahamasApproval")
  const missingEnabledModules: string[] = [];

  if (isNevis && strApprovalWatch && !strModuleEnabledWatch) {
    missingEnabledModules.push("Simplified Tax Return");
  }

  if (isPanama && bfrApprovalWatch && !bfrModuleEnabledWatch) {
    missingEnabledModules.push("Basic Financial Report");
  }

  if (isBahamas && esBahamasApprovalWatch && !esBahamasModuleEnabledWatch) {
    missingEnabledModules.push("Economic Substance");
  }

  if (boDirApprovalWatch && !boDirModuleEnabledWatch) {
    missingEnabledModules.push("Ownership & Officers");
  }

  const hasMissingEnabledModules = missingEnabledModules.length > 0;

  return (
    <>
      <Form formMethods={formMethods} remixFormProps={{ method: "post", className: "h-full", onSubmit: handleSubmit, id: FORM_ID }}>
        <ActionSheetBody>
          <ActionSheetContent title="Edit Company">
            <ActionSheetSection title="Details">
              <ActionSheetDescriptionList
                data={{
                  companyName: company?.name ?? "Unknown",
                  incorporationNumber: company.incorporationNumber,
                  incorporationDate: company?.incorporationDate ? formatDate(company.incorporationDate) : "N/A",
                  masterClientCode: company.masterClientCode,
                  companyNumber: company.code,
                  vpEntityStatus: company.vpEntityStatus,
                  referralOffice: company.referralOffice,
                }}
                headers={[
                  ["companyName", "Entity Name"],
                  ["companyNumber", "VP Code"],
                  ["incorporationNumber", "Incorporation Number"],
                  ["incorporationDate", "Incorporation Date"],
                  ["masterClientCode", "Master Client Code"],
                  ["vpEntityStatus", "VP Entity Status"],
                  ["referralOffice", "Referral Office"],
                ]}
              />
            </ActionSheetSection>
            <div>
              <div className="mb-2 text-blue-700 flex items-center">
                <h4 className="text-xl font-semibold">Approvals</h4>
                <Tooltip delayDuration={0}>
                  <TooltipTrigger asChild>
                    <CircleAlert className="flex shrink-0 size-4 ml-2" />
                  </TooltipTrigger>
                  <TooltipContent className="p-5 space-y-3 font-inter" side="top" align="end">
                    <p>
                      This section confirms release to the Client Portal as per the selected values of relevant modules.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="flex flex-col gap-2 pr-1">
                {isNevis && (
                  <FormSwitch
                    name="strApproval"
                    label="Simplified Tax Return"
                    switchProps={{ withIcon: true, disabled: isSubmitting || company.onboardingStatus === "Declined" || !hasApprovePermission }}
                    formItemProps={{ className: "flex justify-between w-full items-center cursor-pointer" }}
                  />
                )}
                { isPanama && (
                  <FormSwitch
                    name="bfrApproval"
                    label="Basic Financial Report"
                    switchProps={{ withIcon: true, disabled: isSubmitting || company.onboardingStatus === "Declined" || !hasApprovePermission }}
                    formItemProps={{ className: "flex justify-between w-full items-center cursor-pointer" }}
                  />
                )}
                { isBahamas && (
                  <FormSwitch
                    name="esBahamasApproval"
                    label="Economic Substance"
                    switchProps={{ withIcon: true, disabled: isSubmitting || company.onboardingStatus === "Declined" || !hasApprovePermission }}
                    formItemProps={{ className: "flex justify-between w-full items-center cursor-pointer" }}
                  />
                )}
                <FormSwitch
                  name="boDirApproval"
                  label="Ownership & Officers"
                  switchProps={{ withIcon: true, disabled: isSubmitting || company.onboardingStatus === "Declined" || !hasApprovePermission }}
                  formItemProps={{ className: "flex justify-between w-full items-center cursor-pointer" }}
                />
              </div>
            </div>
            <ActionSheetSection title="Enabled Modules" subTitle="The selected modules are visible to the client.">
              <div className="flex flex-col gap-2 pr-1">
                {isNevis && (
                  <>
                    <FormSwitch
                      name="strModuleEnabled"
                      label="Simplified Tax Return"
                      switchProps={{ withIcon: true, disabled: !hasApprovePermission || isSubmitting || company.onboardingStatus === "Declined" }}
                      formItemProps={{ className: "flex justify-between w-full items-center cursor-pointer" }}
                    />
                    <Collapsible open={strModuleEnabledWatch}>
                      <CollapsibleContent>
                        {strModule && displayedYears.length > 0 && (
                          <FormSelect
                            name="submissionYear"
                            label="First Submission Year"
                            selectProps={{ disabled: !hasApprovePermission || isSubmitting || company.onboardingStatus === "Declined" }}
                            options={displayedYears.map(year => <SelectItem key={year} value={String(year)}>{year}</SelectItem>)}
                          />
                        )}
                      </CollapsibleContent>
                    </Collapsible>
                  </>
                )}
                {isPanama && (
                  <>
                    <FormSwitch
                      name="bfrModuleEnabled"
                      label="Basic Financial Report"
                      switchProps={{ withIcon: true, disabled: !hasApprovePermission || isSubmitting || company.onboardingStatus === "Declined" }}
                      formItemProps={{ className: "flex justify-between w-full items-center cursor-pointer" }}
                    />
                    <Collapsible open={bfrModuleEnabledWatch}>
                      <CollapsibleContent>
                        {bfrModule && displayedYears.length > 0 && (
                          <FormInput name="bfrFee" label="Basic Financial Report Fee (USD)" inputProps={{ disabled: isSubmitting || company.onboardingStatus === "Declined", type: "number" }} />
                        )}
                      </CollapsibleContent>
                    </Collapsible>
                  </>
                )}
                {isBahamas && (
                  <FormSwitch
                    name="esBahamasModuleEnabled"
                    label="Economic Substance"
                    switchProps={{ withIcon: true, disabled: !hasApprovePermission || isSubmitting || company.onboardingStatus === "Declined" }}
                    formItemProps={{ className: "flex justify-between w-full items-center cursor-pointer" }}
                  />
                )}
                <FormSwitch
                  name="boDirModuleEnabled"
                  label="Ownership & Officers"
                  switchProps={{ withIcon: true, disabled: !hasApprovePermission || isSubmitting || company.onboardingStatus === "Declined" }}
                  formItemProps={{ className: "flex justify-between w-full items-center cursor-pointer" }}
                />
              </div>
            </ActionSheetSection>
            <ActionSheetSection title="Action">
              <ActionSheetActionRow label="Attention Required">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="self-center text-sm flex items-center justify-center gap-1.5"
                  onClick={() => navigate(`/companies/pending-onboardings/${params.id}/attention-required`)}
                  disabled={isSubmitting}
                >
                  <span className="text-xs font-semibold">Message Employee</span>
                </Button>
              </ActionSheetActionRow>
              <Authorized oneOf={["companies.log.view"]}>
                <ActionSheetActionRow label="History">
                  <Button
                    asChild
                    size="sm"
                    variant="outline"
                    type="button"
                    className="self-center text-sm flex items-center justify-center gap-1.5 mr-2"
                  >
                    <Link to={`/companies/pending-onboardings/${params.id}/log`}>
                      <ScrollText size={14} className="text-blue-600" />
                      <span className="text-xs font-semibold">View Log</span>
                    </Link>
                  </Button>
                </ActionSheetActionRow>
              </Authorized>
            </ActionSheetSection>
          </ActionSheetContent>
          <ActionSheetFooter>
            {company.onboardingStatus !== "Declined" && (
              <>
                <Authorized oneOf={["companies.onboarding.reject"]}>
                  <Button type="button" variant="destructive" onClick={() => navigate(`/companies/pending-onboardings/${params.id}/decline`)}>
                    Decline
                  </Button>
                </Authorized>
                <Authorized oneOf={["companies.onboarding.approve"]}>
                  { (strApprovalWatch || bfrApprovalWatch || esBahamasApprovalWatch) && boDirApprovalWatch
                    ? (
                        <Button type="button" onClick={() => setShowConfirmModal(true)} disabled={isSubmitting}>
                          Approve
                        </Button>
                      )
                    : (
                        <Button type="submit" disabled={isSubmitting}>Save Update</Button>
                      )}
                </Authorized>
              </>
            )}
          </ActionSheetFooter>
        </ActionSheetBody>
      </Form>
      <Authorized oneOf={["companies.onboarding.approve"]}>
        <ConfirmationModal
          isOpen={showConfirmModal}
          onClose={() => setShowConfirmModal(false)}
          formId={FORM_ID}
        >
          <div>
            {
              hasMissingEnabledModules && (
                <Alert title="Missing Modules" variant="error">
                  This will approve the company without enabling all available modules.
                  This may prevent some modules from being displayed in some sections of the Client Portal.
                  Are you sure you wish to proceed?
                </Alert>
              )
            }
          </div>
        </ConfirmationModal>
      </Authorized>
      <Outlet />
    </>
  );
}

export const ErrorBoundary = PageErrorBoundary;
