import type { ReactNode } from "react";
import { But<PERSON> } from "@netpro/design-system";
import { Link, Outlet, useLoaderData } from "@remix-run/react";
import { createColumnHelper } from "@tanstack/react-table";
import { Filter } from "lucide-react";
import { EnhancedTable } from "~/components/EnhancedTable";
import { EnhancedTableContainer } from "~/components/EnhancedTableContainer";
import { PageErrorBoundary } from "~/components/errors/PageErrorBoundary";
import { FilterRow } from "~/components/FilterRow";
import { Form } from "~/components/Form";
import { FormSearch } from "~/components/FormSearch";
import { searchSchema } from "~/features/financial-reports/schemas/search-schema";
import { authHeaders } from "~/lib/auth/utils/auth-headers";
import { useFilterForm } from "~/lib/hooks/useFilterForm";
import { useFormatColDate } from "~/lib/hooks/useFormatColDate";
import { makeEnhancedLoader } from "~/lib/makeEnhancedLoader.server";
import { middleware } from "~/lib/middlewares.server";
import { getFilterParams } from "~/lib/utilities/get-filter-params";
import type { ReportDTO } from "~/services/api-generated";
import { managementGetReportsByType } from "~/services/api-generated";

export const handle = {
  breadcrumb: {
    label: "Financial Reports",
    to: "/simplified-tax-return/financial-reports",
  },
  title: "Financial Reports",
};

export const loader = makeEnhancedLoader(async ({ request, json, queryString }) => {
  await middleware(["auth"], request);
  const schemaData = searchSchema.safeParse(queryString).data
  const { pageNumber, pageSize } = await getFilterParams({ request });
  const financialReportResponse = await managementGetReportsByType({ headers: await authHeaders(request), query: {
    PageNumber: pageNumber,
    PageSize: pageSize,
    ReportTypes: ["Financial"],
    SearchTerm: schemaData?.search,
  } });

  if (!financialReportResponse.data) {
    throw new Response("failed fetch financial reports data")
  }

  return json({ financialReports: financialReportResponse.data });
}, { authorize: ["str.invoices.export"] });

const columnHelper = createColumnHelper<ReportDTO>();
//
function useColumns() {
  const formatColDate = useFormatColDate()

  return [
    columnHelper.display({ id: "name", header: "Report Name", cell: props => props.row.original.reportName }),
    columnHelper.display({ id: "createdAt", header: "Created At", cell: formatColDate("createdAt") }),
    columnHelper.display({ id: "download", header: "Download Link", cell: props => (
      <Button
        asChild
        size="sm"
        variant="outline"
      >
        <Link to={`/downloads/financial-reports/${props.row.original.id}`} reloadDocument>
          <span className="text-xs font-semibold">Download</span>
        </Link>
      </Button>
    ) }),
  ]
}

export default function FinancialReportsComponent(): ReactNode {
  const columns = useColumns()
  const { formMethods } = useFilterForm(searchSchema)
  const { financialReports } = useLoaderData<typeof loader>();

  return (
    <div className="p-2 w-full h-auto flex flex-col">
      <div className="flex flex-col flex-1">
        <Form formMethods={formMethods}>
          <FilterRow>
            <div className="col-span-full flex flex-row items-center gap-2">
              <FormSearch
                name="search"
                formItemProps={{ className: "w-full" }}
                inputProps={{ placeholder: "Search financial reports" }}
              />
              <Button size="sm" className="gap-1.5" type="submit">
                <Filter size={14} />
                Apply Filter(s)
              </Button>
            </div>
          </FilterRow>
        </Form>
        <EnhancedTableContainer>
          <EnhancedTable
            rowId="id"
            columns={columns}
            data={financialReports.data}
            returnURL="/companies/overview"
            totalItems={financialReports.totalItemCount}
          />
        </EnhancedTableContainer>
        <Outlet context={{ financialReports }} />
      </div>
    </div>
  );
}

export const ErrorBoundary = PageErrorBoundary;
