import { z } from "zod";

export const userInviteSchema = z.object({
  userIds: z.array(
    z.string({
      required_error: "User IDs are required.",
    }).uuid({
      message: "Invalid user ID.",
    })
    , { required_error: "You must select at least one user." },
  ).nonempty({ message: "You must select at least one user." }),
});

export type UserInviteSchemaType = z.infer<typeof userInviteSchema>;
